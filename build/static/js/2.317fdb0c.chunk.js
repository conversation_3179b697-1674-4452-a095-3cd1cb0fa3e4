(this.webpackJsonpreact_ts_template=this.webpackJsonpreact_ts_template||[]).push([[2],{130:function(e,t,n){"use strict";n(108),n(380),n(382)},131:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=h(n(106)),a=h(n(91)),o=h(n(87)),l=h(n(90)),u=h(n(88)),i=h(n(89)),s=h(n(96)),c=y(n(12)),d=y(n(0)),f=h(n(136)),p=n(153),m=h(n(383)),b=h(n(387));function y(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t.default=e,t}function h(e){return e&&e.__esModule?e:{default:e}}var v=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var a=0;for(r=Object.getOwnPropertySymbols(e);a<r.length;a++)t.indexOf(r[a])<0&&(n[r[a]]=e[r[a]])}return n};function C(){}function g(e){return"undefined"===typeof e||null===e?"":e+""}var k=function(e){function t(e){(0,o.default)(this,t);var n=(0,u.default)(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));return n.onInputChange=function(e){var t=e.target,r=t.value,a=0;try{a=t.selectionEnd||0}catch(c){console.warn("Get selection error:",c)}var o=n.state.value,l=void 0===o?"":o,u=n.props.type,i=r;switch(u){case"bankCard":i=r.replace(/\D/g,"").replace(/(....)(?=.)/g,"$1 ");break;case"phone":var s=(i=r.replace(/\D/g,"").substring(0,11)).length;s>3&&s<8?i=i.substr(0,3)+" "+i.substr(3):s>=8&&(i=i.substr(0,3)+" "+i.substr(3,4)+" "+i.substr(7));break;case"number":i=r.replace(/\D/g,"")}n.handleOnChange(i,i!==r,(function(){switch(u){case"bankCard":case"phone":case"number":try{var e=n.calcPos(a,l,r,i,[" "],/\D/g);("phone"===u&&(4===e||9===e)||"bankCard"===u&&e>0&&e%5===0)&&(e-=1),t.selectionStart=t.selectionEnd=e}catch(c){console.warn("Set selection error:",c)}}}))},n.handleOnChange=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:C,a=n.props.onChange;"value"in n.props?n.setState({value:n.props.value}):n.setState({value:e}),a?t?setTimeout((function(){a(e),r()})):(a(e),r()):r()},n.onInputFocus=function(e){n.debounceTimeout&&(clearTimeout(n.debounceTimeout),n.debounceTimeout=null),n.setState({focus:!0}),n.props.onFocus&&n.props.onFocus(e)},n.onInputBlur=function(e){n.inputRef&&(n.debounceTimeout=window.setTimeout((function(){document.activeElement!==(n.inputRef&&n.inputRef.inputRef)&&n.setState({focus:!1})}),200)),n.props.onBlur&&(setTimeout((function(){document.body&&(document.body.scrollTop=document.body.scrollTop)}),100),n.props.onBlur(e))},n.clearInput=function(){"password"!==n.props.type&&n.props.updatePlaceholder&&n.setState({placeholder:n.props.value}),n.setState({value:""}),n.props.onChange&&n.props.onChange(""),n.focus()},n.focus=function(){n.inputRef&&n.inputRef.focus()},n.calcPos=function(e,t,n,r,a,o){var l=n.length-t.length,u=e;if(l>0){var i=n.substr(u-l,l).replace(o,"").length;u-=l-i;for(var s=0;i>0;)-1===a.indexOf(r.charAt(u-i+s))?i--:s++;u+=s}return u},n.state={placeholder:e.placeholder,value:g(e.value||e.defaultValue)},n}return(0,i.default)(t,e),(0,l.default)(t,[{key:"componentWillReceiveProps",value:function(e){"placeholder"in e&&!e.updatePlaceholder&&this.setState({placeholder:e.placeholder}),"value"in e&&this.setState({value:e.value})}},{key:"componentWillUnmount",value:function(){this.debounceTimeout&&(window.clearTimeout(this.debounceTimeout),this.debounceTimeout=null)}},{key:"render",value:function(){var e,t,o=this,l=(0,a.default)({},this.props);delete l.updatePlaceholder;var u=l.prefixCls,i=l.prefixListCls,c=l.editable,y=l.style,h=l.clear,C=l.children,k=l.error,K=l.className,_=l.extra,O=l.labelNumber,P=l.type,x=l.onExtraClick,I=l.onErrorClick,E=l.moneyKeyboardAlign,w=l.moneyKeyboardWrapProps,L=l.moneyKeyboardHeader,j=l.onVirtualKeyboardConfirm,B=l.autoAdjustHeight,N=l.disabledKeys,S=v(l,["prefixCls","prefixListCls","editable","style","clear","children","error","className","extra","labelNumber","type","onExtraClick","onErrorClick","moneyKeyboardAlign","moneyKeyboardWrapProps","moneyKeyboardHeader","onVirtualKeyboardConfirm","autoAdjustHeight","disabledKeys"]),R=S.name,F=S.disabled,A=S.maxLength,M=this.state.value,T=(0,p.getComponentLocale)(this.props,this.context,"InputItem",(function(){return n(388)})),H=T.confirmLabel,D=T.backspaceLabel,W=T.cancelKeyboardLabel,V=this.state,U=V.focus,J=V.placeholder,q=(0,s.default)(i+"-item",u+"-item",i+"-item-middle",K,(e={},(0,r.default)(e,u+"-disabled",F),(0,r.default)(e,u+"-error",k),(0,r.default)(e,u+"-focus",U),(0,r.default)(e,u+"-android",U),e)),G=(0,s.default)(u+"-label",(t={},(0,r.default)(t,u+"-label-2",2===O),(0,r.default)(t,u+"-label-3",3===O),(0,r.default)(t,u+"-label-4",4===O),(0,r.default)(t,u+"-label-5",5===O),(0,r.default)(t,u+"-label-6",6===O),(0,r.default)(t,u+"-label-7",7===O),t)),$=u+"-control",z="text";"bankCard"===P||"phone"===P?z="tel":"password"===P?z="password":"digit"===P?z="number":"text"!==P&&"number"!==P&&(z=P);var Q=void 0;"number"===P&&(Q={pattern:"[0-9]*"});var X=void 0;return"digit"===P&&(X={className:"h5numInput"}),d.createElement("div",{className:q},d.createElement("div",{className:i+"-line"},C?d.createElement("div",{className:G},C):null,d.createElement("div",{className:$},"money"===P?d.createElement(m.default,{value:g(M),type:P,ref:function(e){return o.inputRef=e},maxLength:A,placeholder:J,onChange:this.onInputChange,onFocus:this.onInputFocus,onBlur:this.onInputBlur,onVirtualKeyboardConfirm:j,disabled:F,editable:c,prefixCls:u,style:y,confirmLabel:H,backspaceLabel:D,cancelKeyboardLabel:W,moneyKeyboardAlign:E,moneyKeyboardWrapProps:w,moneyKeyboardHeader:L,autoAdjustHeight:B,disabledKeys:N}):d.createElement(b.default,(0,a.default)({},Q,S,X,{value:g(M),defaultValue:void 0,ref:function(e){return o.inputRef=e},style:y,type:z,maxLength:A,name:R,placeholder:J,onChange:this.onInputChange,onFocus:this.onInputFocus,onBlur:this.onInputBlur,readOnly:!c,disabled:F}))),h&&c&&!F&&M&&(""+M).length>0?d.createElement(f.default,{activeClassName:u+"-clear-active"},d.createElement("div",{className:u+"-clear",onClick:this.clearInput})):null,k?d.createElement("div",{className:u+"-error-extra",onClick:I}):null,""!==_?d.createElement("div",{className:u+"-extra",onClick:x},_):null))}}]),t}(d.Component);k.defaultProps={prefixCls:"am-input",prefixListCls:"am-list",type:"text",editable:!0,disabled:!1,placeholder:"",clear:!1,onChange:C,onBlur:C,onFocus:C,extra:"",onExtraClick:C,error:!1,onErrorClick:C,onVirtualKeyboardConfirm:C,labelNumber:5,updatePlaceholder:!1,moneyKeyboardAlign:"right",moneyKeyboardWrapProps:{},moneyKeyboardHeader:null,disabledKeys:null},k.contextTypes={antLocale:c.object},t.default=k,e.exports=t.default},220:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=t.canUseDOM=!("undefined"===typeof window||!window.document||!window.document.createElement);t.IS_IOS=r&&/iphone|ipad|ipod/i.test(window.navigator.userAgent)},380:function(e,t,n){"use strict";n(108),n(381)},381:function(e,t,n){},382:function(e,t,n){},383:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=b(n(87)),a=b(n(90)),o=b(n(88)),l=b(n(89)),u=b(n(96)),i=m(n(0)),s=m(n(15)),c=n(384),d=b(n(385)),f=b(n(386)),p=n(220);function m(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t.default=e,t}function b(e){return e&&e.__esModule?e:{default:e}}var y=null,h=!!s.createPortal;var v=function(e){function t(e){(0,r.default)(this,t);var n=(0,o.default)(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));return n.onChange=function(e){"value"in n.props||n.setState({value:e.target.value}),n.props.onChange(e)},n.onConfirm=function(e){n.props.onVirtualKeyboardConfirm(e)},n.addBlurListener=function(){document.addEventListener("click",n.doBlur,!1)},n.removeBlurListener=function(){document.removeEventListener("click",n.doBlur,!1)},n.saveRef=function(e){h&&e&&(y=e)},n.doBlur=function(e){var t=n.state.value;e.target!==n.inputRef&&n.onInputBlur(t)},n.unLinkInput=function(){y&&y.antmKeyboard&&y.linkedInput&&y.linkedInput===n&&(y.linkedInput=null,n.props.autoAdjustHeight&&(n.getContainer().style.height="0"),(0,c.addClass)(y.antmKeyboard,n.props.keyboardPrefixCls+"-wrapper-hide")),n.removeBlurListener()},n.onInputBlur=function(e){h&&(n.keyBoard=null),n.state.focus&&(n.setState({focus:!1}),n.props.onBlur(e),setTimeout((function(){n.unLinkInput()}),50))},n.onInputFocus=function(){var e=n.state.value;n.props.onFocus(e),n.setState({focus:!0},(function(){if(y){if(y.linkedInput=n,y.antmKeyboard){if(n.props.autoAdjustHeight){var t=y.antmKeyboard.offsetHeight;if(n.getContainer().style.height=t+"px",n.inputRef){var r=n.inputRef.getBoundingClientRect().bottom,a=window.innerHeight-r;a<t&&(o=function(){var e=document.scrollingElement||document.documentElement;return e&&e.scrollTop||0}()+t-a,(document.scrollingElement||document.documentElement).scrollTop=o)}}(0,c.removeClass)(y.antmKeyboard,n.props.keyboardPrefixCls+"-wrapper-hide")}y.confirmDisabled=""===e,y.confirmKeyboardItem&&(""===e?(0,c.addClass)(y.confirmKeyboardItem,n.props.keyboardPrefixCls+"-item-disabled"):(0,c.removeClass)(y.confirmKeyboardItem,n.props.keyboardPrefixCls+"-item-disabled"))}var o}))},n.onKeyboardClick=function(e){var t=n.props.maxLength,r=n.state.value,a=n.onChange,o=void 0;"delete"===e?a({target:{value:o=r.substring(0,r.length-1)}}):"confirm"===e?(a({target:{value:o=r}}),n.onInputBlur(r),n.onConfirm(r)):"hide"===e?(o=r,n.onInputBlur(o)):a(void 0!==t&&+t>=0&&(r+e).length>t?{target:{value:o=(r+e).substr(0,t)}}:{target:{value:o=r+e}}),y&&(y.confirmDisabled=""===o,y.confirmKeyboardItem&&(""===o?(0,c.addClass)(y.confirmKeyboardItem,n.props.keyboardPrefixCls+"-item-disabled"):(0,c.removeClass)(y.confirmKeyboardItem,n.props.keyboardPrefixCls+"-item-disabled")))},n.onFakeInputClick=function(){n.focus()},n.focus=function(){n.renderCustomKeyboard(),n.removeBlurListener(),n.state.focus||n.onInputFocus(),setTimeout((function(){n.addBlurListener()}),50)},n.state={focus:!1,value:e.value||""},n}return(0,l.default)(t,e),(0,a.default)(t,[{key:"componentWillReceiveProps",value:function(e){"value"in e&&this.setState({value:e.value})}},{key:"componentWillUnmount",value:function(){this.state.focus&&this.props.onBlur(this.state.value),this.unLinkInput()}},{key:"getComponent",value:function(){var e=this.props,t=e.confirmLabel,n=e.backspaceLabel,r=e.cancelKeyboardLabel,a=e.keyboardPrefixCls,o=e.moneyKeyboardWrapProps,l=e.moneyKeyboardHeader,u=e.disabledKeys;return i.createElement(d.default,{ref:this.saveRef,onClick:this.onKeyboardClick,prefixCls:a,confirmLabel:t,backspaceLabel:n,cancelKeyboardLabel:r,wrapProps:o,header:l,disabledKeys:u})}},{key:"getContainer",value:function(){var e=this.props.keyboardPrefixCls,t=document.querySelector("#"+e+"-container");return t||((t=document.createElement("div")).setAttribute("id",e+"-container"),document.body.appendChild(t)),this.container=t,this.container}},{key:"renderCustomKeyboard",value:function(){var e=this;h?this.keyBoard=i.createElement(f.default,{getContainer:function(){return e.getContainer()}},this.getComponent()):y=s.unstable_renderSubtreeIntoContainer(this,this.getComponent(),this.getContainer())}},{key:"renderPortal",value:function(){return h&&p.canUseDOM?this.keyBoard:null}},{key:"render",value:function(){var e=this,t=this.props,n=t.placeholder,r=t.disabled,a=t.editable,o=t.moneyKeyboardAlign,l=this.state,s=l.focus,c=l.value,d=r||!a,f=(0,u.default)("fake-input",{focus:s,"fake-input-disabled":r}),p=(0,u.default)("fake-input-container",{"fake-input-container-left":"left"===o});return i.createElement("div",{className:p},""===c&&i.createElement("div",{className:"fake-input-placeholder"},n),i.createElement("div",{role:"textbox","aria-label":c||n,className:f,ref:function(t){return e.inputRef=t},onClick:d?function(){}:this.onFakeInputClick},c),this.renderPortal())}}]),t}(i.Component);v.defaultProps={onChange:function(){},onFocus:function(){},onBlur:function(){},onVirtualKeyboardConfirm:function(){},placeholder:"",disabled:!1,editable:!0,prefixCls:"am-input",keyboardPrefixCls:"am-number-keyboard",autoAdjustHeight:!1},t.default=v,e.exports=t.default},384:function(e,t,n){"use strict";function r(e,t){return e.classList?e.classList.contains(t):(" "+e.className+" ").indexOf(" "+t+" ")>-1}Object.defineProperty(t,"__esModule",{value:!0}),t.hasClass=r,t.addClass=function(e,t){e.classList?e.classList.add(t):r(e,t)||(e.className=e.className+" "+t)},t.removeClass=function(e,t){if(e.classList)e.classList.remove(t);else if(r(e,t)){var n=e.className;e.className=(" "+n+" ").replace(" "+t+" ","")}}},385:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.KeyboardItem=void 0;var r=p(n(91)),a=p(n(106)),o=p(n(87)),l=p(n(90)),u=p(n(88)),i=p(n(89)),s=p(n(96)),c=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t.default=e,t}(n(0)),d=p(n(136)),f=n(220);function p(e){return e&&e.__esModule?e:{default:e}}var m=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var a=0;for(r=Object.getOwnPropertySymbols(e);a<r.length;a++)t.indexOf(r[a])<0&&(n[r[a]]=e[r[a]])}return n};function b(e,t){if(!e||!e.length||!t)return!1;for(var n=0,r=e.length;n<r;n++)if(e[n]===t)return!0;return!1}var y=t.KeyboardItem=function(e){function t(){return(0,o.default)(this,t),(0,u.default)(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return(0,i.default)(t,e),(0,l.default)(t,[{key:"render",value:function(){var e=this.props,t=e.prefixCls,n=e.onClick,o=e.className,l=e.disabled,u=e.children,i=e.tdRef,f=e.label,p=e.iconOnly,b=m(e,["prefixCls","onClick","className","disabled","children","tdRef","label","iconOnly"]),y=u;"keyboard-delete"===o?y="delete":"keyboard-hide"===o?y="hide":"keyboard-confirm"===o&&(y="confirm");var h=(0,a.default)({},t+"-item-disabled",l),v=(0,s.default)(t+"-item",o,h);return c.createElement(d.default,{disabled:l,activeClassName:t+"-item-active"},c.createElement("td",(0,r.default)({ref:i,onClick:function(e){n(e,y)},className:v},b),u,p&&c.createElement("i",{className:"sr-only"},f)))}}]),t}(c.Component);y.defaultProps={prefixCls:"am-number-keyboard",onClick:function(){},disabled:!1};var h=function(e){function t(){(0,o.default)(this,t);var e=(0,u.default)(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments));return e.onKeyboardClick=function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return t.nativeEvent.stopImmediatePropagation(),e.props.disabledKeys&&b(e.props.disabledKeys,n)||"confirm"===n&&e.confirmDisabled?null:void(e.linkedInput&&e.linkedInput.onKeyboardClick(n))},e.renderKeyboardItem=function(t,n){var r=!1;return e.props.disabledKeys&&b(e.props.disabledKeys,t)&&(r=!0),c.createElement(y,{onClick:e.onKeyboardClick,key:"item-"+t+"-"+n,disabled:r},t)},e}return(0,i.default)(t,e),(0,l.default)(t,[{key:"render",value:function(){var e=this,t=this.props,n=t.prefixCls,a=t.confirmLabel,o=t.backspaceLabel,l=t.cancelKeyboardLabel,u=t.wrapProps,i=t.header,d=(0,s.default)(n+"-wrapper",n+"-wrapper-hide");return c.createElement("div",(0,r.default)({className:d,ref:function(t){return e.antmKeyboard=t}},u),i&&c.cloneElement(i,{onClick:this.onKeyboardClick}),c.createElement("table",null,c.createElement("tbody",null,c.createElement("tr",null,["1","2","3"].map((function(t,n){return e.renderKeyboardItem(t,n)})),c.createElement(y,(0,r.default)({className:"keyboard-delete",rowSpan:2,onClick:this.onKeyboardClick},this.getAriaAttr(o)))),c.createElement("tr",null,["4","5","6"].map((function(t,n){return e.renderKeyboardItem(t,n)}))),c.createElement("tr",null,["7","8","9"].map((function(t,n){return e.renderKeyboardItem(t,n)})),c.createElement(y,{className:"keyboard-confirm",rowSpan:2,onClick:this.onKeyboardClick,tdRef:function(t){return e.confirmKeyboardItem=t}},a)),c.createElement("tr",null,[".","0"].map((function(t,n){return e.renderKeyboardItem(t,n)})),c.createElement(y,(0,r.default)({className:"keyboard-hide",onClick:this.onKeyboardClick},this.getAriaAttr(l)))))))}},{key:"getAriaAttr",value:function(e){return f.IS_IOS?{label:e,iconOnly:!0}:{role:"button","aria-label":e}}}]),t}(c.Component);h.defaultProps={prefixCls:"am-number-keyboard",disabledKeys:null},t.default=h},386:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=s(n(87)),a=s(n(90)),o=s(n(88)),l=s(n(89)),u=i(n(0));function i(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t.default=e,t}function s(e){return e&&e.__esModule?e:{default:e}}var c=i(n(15)).createPortal,d=function(e){function t(e){(0,r.default)(this,t);var n=(0,o.default)(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));return n.container=n.props.getContainer(),n}return(0,l.default)(t,e),(0,a.default)(t,[{key:"render",value:function(){return this.props.children?c(this.props.children,this.container):null}}]),t}(u.Component);t.default=d,e.exports=t.default},387:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=s(n(91)),a=s(n(87)),o=s(n(90)),l=s(n(88)),u=s(n(89)),i=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t.default=e,t}(n(0));function s(e){return e&&e.__esModule?e:{default:e}}var c=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var a=0;for(r=Object.getOwnPropertySymbols(e);a<r.length;a++)t.indexOf(r[a])<0&&(n[r[a]]=e[r[a]])}return n},d=function(e){function t(){(0,a.default)(this,t);var e=(0,l.default)(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments));return e.onInputBlur=function(t){var n=t.target.value;e.props.onBlur&&e.props.onBlur(n)},e.onInputFocus=function(t){var n=t.target.value;e.props.onFocus&&e.props.onFocus(n)},e.focus=function(){e.inputRef&&e.inputRef.focus()},e}return(0,u.default)(t,e),(0,o.default)(t,[{key:"render",value:function(){var e=this,t=this.props,n=(t.onBlur,t.onFocus,c(t,["onBlur","onFocus"]));return i.createElement("input",(0,r.default)({ref:function(t){return e.inputRef=t},onBlur:this.onInputBlur,onFocus:this.onInputFocus},n))}}]),t}(i.Component);t.default=d,e.exports=t.default},388:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={confirmLabel:"\u786e\u5b9a",backspaceLabel:"\u9000\u683c",cancelKeyboardLabel:"\u6536\u8d77\u952e\u76d8"},e.exports=t.default}}]);