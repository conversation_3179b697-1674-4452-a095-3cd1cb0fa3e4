/*
 * @Author: w<PERSON><PERSON><PERSON>
 * @Date: 2023-08-08 09:15:28
 * @LastEditors: wuqi<PERSON>
 * @LastEditTime: 2024-07-18 15:20:20
 */
/* 环境变量 */
type EnvConfig = {
  ENV_TYPE: 'test' | 'staging' | 'production'
  BASE_URL: string
}

/* 用户数据 */
type AppUserInfo = {
  token: string
  user_no: number
  insurance_user_id: string
  nickname: string
  head_img
}

type DeviceInfor = {
  // isBuryPoint: boolean, // 是否支持埋点（针对指纹库有兼容性问题无法获取设备信息时
  quid: string, // 设备id,通过指纹库获取
  from: string, // 渠道来源，默认为'0'，否则为query里点channel值
  source: string, // 当前页路径
  timestamp: string, // 时间戳
  system: string, // 系统平台
  requestid: string, // 请求id，通过uuid来生成
  action: string, // 动作
  desc: string, // 如果有action，则desc代表触发action的元素
}