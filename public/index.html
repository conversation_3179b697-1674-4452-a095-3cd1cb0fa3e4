<!--
 * @Author: w<PERSON><PERSON><PERSON>
 * @Date: 2023-08-08 09:15:28
 * @LastEditors: wuqi<PERSON>
 * @LastEditTime: 2024-07-16 16:32:32
-->
<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, shrink-to-fit=no,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no"
    />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="Web site created using create-react-app" />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo.png" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <title>退役军人家庭专属养老金</title>
    <script src="https://as.alipayobjects.com/g/component/fastclick/1.0.6/fastclick.js"></script>
    <script>
      // if ('addEventListener' in document) {
      //   document.addEventListener(
      //     'DOMContentLoaded',
      //     function () {
      //       FastClick.attach(document.body)
      //     },
      //     false
      //   )
      // }
      // 部分安卓手机不支持promise，引入promise polyfill
      if (!window.Promise) {
        document.writeln(
          '<script src="https://as.alipayobjects.com/g/component/es6-promise/3.2.2/es6-promise.min.js"' +
            '>' +
            '<' +
            '/' +
            'script>'
        )
      }
    </script>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <script src="//res2.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
</html>
