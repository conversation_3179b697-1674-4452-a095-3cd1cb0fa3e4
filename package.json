{"name": "react_ts_template", "version": "1.0.0", "private": true, "dependencies": {"@fingerprintjs/fingerprintjs": "^3.3.0", "@testing-library/jest-dom": "^4.2.4", "@testing-library/react": "^9.3.2", "@testing-library/user-event": "^7.1.2", "@types/express": "^4.17.6", "@types/jest": "^24.0.0", "@types/mockjs": "^1.0.2", "@types/node": "^12.0.0", "@types/react": "^16.9.0", "@types/react-dom": "^16.9.0", "@types/react-redux": "^7.1.9", "@types/react-router-dom": "^5.1.5", "antd-mobile": "^2.3.3", "axios": "^0.19.2", "babel-plugin-import": "^1.13.0", "canvas": "^2.11.2", "customize-cra": "^1.0.0", "dayjs": "^1.11.11", "dotenv-cli": "^3.1.0", "express": "^4.17.1", "http-proxy-middleware": "^1.0.4", "mockjs": "^1.1.0", "node-sass": "^4.14.1", "pdfh5": "^1.4.9", "postcss-px2rem": "^0.3.0", "qs": "^6.12.0", "react": "^16.13.1", "react-app-rewired": "^2.1.6", "react-dom": "^16.13.1", "react-loadable": "^5.5.0", "react-redux": "^7.2.0", "react-router-dom": "^5.2.0", "react-scripts": "3.4.1", "react-sticky": "^6.0.3", "redux": "^4.0.5", "redux-logger": "^3.0.6", "redux-thunk": "^2.3.0", "typescript": "^5.5.3", "uuid": "^8.3.2", "vconsole": "^3.15.1", "webpack-bundle-analyzer": "^3.8.0"}, "scripts": {"start": "react-app-rewired start", "mock": "node --experimental-modules src/mock/server.mjs", "build:dev": "dotenv -e .env.development react-app-rewired build", "build:sta": "dotenv -e .env.staging react-app-rewired build", "build:pro": "dotenv -e .env.production react-app-rewired build", "test": "react-app-rewired test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "homepage": ".", "devDependencies": {"@types/react-router-config": "^5.0.1"}}