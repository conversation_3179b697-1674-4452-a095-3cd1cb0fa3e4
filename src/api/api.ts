import request from '@/api/request'


interface SearchParam {
  username: string, id_no: string, mobile: string
}

export interface SearchResponseDataLocal {
  applicant_name: string; // 投保人姓名
  applicant_idno: string; // 投保人证件号
  policy_list: SearchResponseData[]; // 保单列表
}

export interface SearchResponseData {
  policy_code: string; // 保单号
  policy_status: string; // 保单责任状态 1有效 2失效 3终止
  apply_date: string; // 投保日期
  effect_date: string; // 生效日期
  end_date: string; // 终止日期
  first_premium: string; // 首期保费 单位：元
  first_charge_type: string; // 首期缴费方式 1年交 2半年交 3季交 4月交 5趸交(一次付清)
  pay_type: string; // 缴费方式 银行卡 微信
  addup_status: string; // 是否开启定投 1是  2否
  addup_type: string; // 定投频率 1年 2半年 3季度 4月
  addup_amount: string; // 定投追加保费 单位：元
  is_tax_policy: string; // 是否个养保单 Y是 N否
  total_payment_amt: string; // 累计已交保费 单位：元
  policy_account_value: string; // 保单账户价值 单位：元 返回保单下所有投资账户的总金额
  applicant_name: string; // 投保人姓名
  applicant_mobile: string; // 投保人手机号
  applicant_id_type: string; // 投保人证件类型 1身份证 7户口簿
  applicant_idno: string; // 投保人证件号码
  insurant_name: string; // 被保人姓名
  insurant_mobile: string; // 被保人手机号
  insurant_id_type: string; // 被保人证件类型 1身份证 7户口簿
  insurant_idno: string; // 被保人证件号码
}

export interface RootObject {
  code: number;
  data: SearchResponseData[];
  msg: string;
  trace_id: string;
}

export interface RootObjectLocal {
  code: number;
  data: SearchResponseDataLocal[];
  msg: string;
  trace_id: string;
}

export interface CalcParam {
  province: string; // 省份
  city: string; // 城市
  district: string; // 区县
  month_basic: number; // 月缴保费基数 单位：元
  month_avg: number; // 月平均工资 单位：元
  balance: number; // 账户存储额 单位：元
  expoint: number; // 缴费指数
  year_count: number; // 缴费年限
  month_count: number; // 计发月数
  result: number; // 养老金计算结果
}

class Api {
  static getBanner = (params: any): Promise<any> =>
    request(`/tongji/banner?platform=${params?.platform}`, 'POST', {}, true)

  static getUserInfo = (params: any): Promise<any> =>
    request(`/user/info`, 'POST', {}, true)

  static getOrderList = (params: SearchParam): Promise<RootObject> => request(`insur_cii/api/yuanxin/order_list`, 'POST', params, true)

  static getOrderListLocal = (params?: SearchParam): Promise<RootObjectLocal> => request(`insur_cii/api/yuanxin/order_list_local`, 'POST', {})

  static calcuSave = (params: CalcParam): Promise<any> => request('insur_cii/api/api/calcu/save', 'POST', params, true)

  static calcuGet = () => request('insur_cii/api/calcu/get', 'GET')
}
export default Api;