/*
 * @Author: w<PERSON><PERSON><PERSON>
 * @Date: 2023-08-08 09:15:28
 * @LastEditors: wuqiang
 * @LastEditTime: 2024-07-18 17:42:49
 */
import React, { useEffect } from 'react'
import RouterView from './router'
import request from '@/api/request'
import qs from 'qs'
import { isWechat } from '@/utils'
import { Toast } from 'antd-mobile'
import VConsole from 'vconsole';
import './App.scss'
import { useDispatch } from 'react-redux'
import { SET_USER_INFO } from './constants'
const local: any = window.location
const search = local.search.slice(1)
const code: string = (qs.parse(search) as any)['code']
const appid = 'wx47eb7cb22dd360e3'

function App() {
  // 取消静默登录
  // const dispatch = useDispatch();
  // const getCode = async () => {
  //   // 非静默授权，第一次有弹框
  //   if (process.env.NODE_ENV === 'development') return
  //   if (!localStorage.getItem('token')) {
  //     if (!code) {
  //       window.location.href =
  //         'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' +
  //         appid +
  //         '&redirect_uri=' +
  //         encodeURIComponent(local.href) +
  //         '&response_type=code&scope=snsapi_userinfo&state=1#wechat_redirect'
  //     } else {
  //       const { data, error } = await request(`/insur_cii/api/wechat/auth_login`, 'GET', { code }, true);
  //       if (!error) {
  //         dispatch({ type: SET_USER_INFO, value: data || {} });
  //         localStorage.setItem('token', data?.token || '')
  //         // alert(JSON.stringify(data))
  //       } else {
  //         Toast.info(error || '当前同时在线人数过多，请您稍后查看!', 2)
  //       }
  //     }
  //   } else {
  //     const { data, error } = await request(`/insur_cii/api/user/info`, 'POST', {}, true);
  //     if (!error) {
  //       dispatch({ type: SET_USER_INFO, value: { ...data, token: localStorage.getItem('token') } })
  //     } else {
  //       Toast.info(error || '当前同时在线人数过多，请您稍后查看!', 2)
  //     }
  //   }
  // }

  // useEffect(() => {
  //   if (isWechat()) {
  //     getCode()
  //   }
  //   // new VConsole();
  // }, [])

  return <RouterView></RouterView>
}

export default App
