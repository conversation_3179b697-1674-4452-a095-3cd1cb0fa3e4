/*
 * @Author: w<PERSON><PERSON><PERSON>
 * @Date: 2023-08-08 09:15:28
 * @LastEditors: wuqiang
 * @LastEditTime: 2024-07-18 15:20:45
 */
import { combineReducers } from 'redux'

import user from './user'
import login from './login'
import insure from './insure';
import insureInfo from './insureInfo'
import deviceInfor from './deviceInfor';

export default combineReducers({
  user,
  login,
  insure,
  insureInfo,
  deviceInfor
})
