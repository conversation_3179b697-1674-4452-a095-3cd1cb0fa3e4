/*
 * @Author: w<PERSON><PERSON><PERSON>
 * @Date: 2023-08-08 09:15:28
 * @LastEditors: wuqi<PERSON>
 * @LastEditTime: 2024-07-18 14:50:33
 */
import { SET_INSURE_INFO, RESET_INSURE_INFO } from '@/constants/index'

const INITIAL_STATE = {
  isReadRule: true, // 是否阅读了协议，
  isLegalBenefit: true, // 是否法定受益人
  question1: 1, // 第一题是否满足要求0为默认否
  question2: 0, // 第二题是否超过家庭收入的20%，1为默认是,
  order_no: '', // 投保接口返回的订单号
  intelligent_url: '', // 投保接口返回的智能核保地址,
  isFromRule: false, // 是否是从协议查看页返回
}

export default function insureInfo(state = INITIAL_STATE, action: { type: string; value: any }) {
  switch (action.type) {
    case SET_INSURE_INFO:
      return {
        ...state,
        ...action.value
      }
    case RESET_INSURE_INFO:
      return INITIAL_STATE;
    default:
      return state
  }
}
