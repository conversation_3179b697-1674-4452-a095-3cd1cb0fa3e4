/*
 * @Author: your name
 * @Date: 2021-11-22 11:12:45
 * @LastEditTime: 2024-07-18 15:35:15
 * @LastEditors: wuqiang
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: /recoms-is-master/Users/<USER>/workspace/huijun-frontend/src/store/reducers/blueCard.ts
 */
const SET_DEVICE_INFOR = 'SET_DEVICE_INFOR';

export const INITIAL_STATE: DeviceInfor = {
  // isBuryPoint: false,
  quid: '',
  from: '0',
  source: '',
  timestamp: '',
  system: '',
  requestid: '',
  action: '',
  desc: '',
}

export default function deviceInfor(state = INITIAL_STATE, action: { type: string; value: any }) {
  switch (action.type) {
    case SET_DEVICE_INFOR:
      return {
        ...state,
        ...action.value
      }
    default:
      return state
  }
}