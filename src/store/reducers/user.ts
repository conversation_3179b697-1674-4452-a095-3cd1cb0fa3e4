/*
 * @Author: wuqi<PERSON>
 * @Date: 2023-08-08 09:15:28
 * @LastEditors: wuqiang
 * @LastEditTime: 2024-02-27 16:21:46
 */
import { RESET_USER_INFO, SET_USER_INFO } from '@/constants/index'

export const INITIAL_STATE: AppUserInfo = {
  token: '',
  user_no: 0,
  insurance_user_id: '',
  nickname: '',
  head_img: ''
}

export default function user(state = INITIAL_STATE, action: { type: string; value: any }) {
  switch (action.type) {
    case SET_USER_INFO:
      return {
        ...state,
        ...action.value
      }
    case RESET_USER_INFO:
      return INITIAL_STATE;
    default:
      return state
  }
}
