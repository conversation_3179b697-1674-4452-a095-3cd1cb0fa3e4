/*
 * @Author: w<PERSON><PERSON><PERSON>
 * @Date: 2023-08-08 09:15:28
 * @LastEditors: wu<PERSON><PERSON>
 * @LastEditTime: 2024-09-20 10:42:44
 */
import { SET_INSURE_DETAIL, RESET_INSURE_DETAIL } from '@/constants/index'

export const BENEFIT = {
  "index": 0,
  "user_type":3, // // 1被保人 2投保人 3受益人
  "relation":2, // 被保人和投保人关系 或者 受益人与被保人关系  0-本人 1-配偶关系 2-子女 3-父母关系
  "user_name": "",
  "mobile": "",
  "id_type" : 1, // 证件类型 1身份证 7户口本  默认身份证 关系类型是子女时 可选户口簿
  "id_code" : "", // 证件号
  "id_val_date" : "", // 证件有效期始
  "id_exp_date" : "", // 证件失效期止  证件有效期为长期时，失效日期默认传“9999-12-31”
  "occupation_type" : "", // // 职业代码
  "contact_info" : { // 联系信息 用户类型为 投保人和受益人 需要填写
      "address_type" : 1, 		 // 通讯地址类型 1现/常住地址 27工作单位地址 100户籍地址
      "region_code" : "", 		//  地区码 只传3级编码
      "address": "" // 详细地址
  },
  "benefit_info" : { // 用户类型为受益人时填写
      "order" :1,   // 受益顺序
      "percent" : 100 // 受益比率
  }
}

export const APPLICANT = {
  "index": 2,
  "user_type":2,
  "relation":0,
  "user_name": "",
  "mobile": "",
  "id_type" : 1,
  "id_code" : "",
  "id_val_date" : "",
  "id_exp_date" : "",
  "occupation_type" : "",
  "contact_info" : {
      "address_type" : 1,
      "region_code" : "",
      "address": ""
  },
  
  // 保额超过50万触发反洗钱时必传的影像资料,
  // 若投、被保人为同一人，仅投保人提供， 否则 投保人和被保人都需要提供
  "image_list" : [ 
      {
          "image_type": 1, // 1-身份证 7-户口簿 8-关系证明
          //"relation_prove_type": "", // 类型是关系证明时，这里需要说明关系证明类型  1-户口本，2-结婚证、3-出生证，4-亲子鉴定 、5-其它法律部门开的证明
          "img_url_1" : "",  // 身份证时：正面影像  户口簿时：被保人页  关系证明时：按需传
          "img_url_2" : ""   // 身份证时：反面影像  户口簿时：投保人页  关系证明时：按需传
          //"img_url_3" : "" // 身份证时：空       户口簿时：户主页    关系证明时：按需传
      }
  ],
};

export const INITIAL_STATE = {
  "insure_plan" : {
      "brith_day" : "2020-01-01", // 被保人生日 参数省略  后端从被保人身份证获取，前端应该和被保人身份证做校验
      "gender" : "M", // 被保人性别 男M 女F      参数省略  后端从被保人身份证获取，前端应该和被保人身份证做校验
      "insure_term_type": 2, // 保险期间  1保至70周岁 2保终身   传1或2
      "insure_amount_n" : 10, // 保险金额  单位万
      "insure_pay_dura" : 20, // 保险缴费期限 10年交 20年交     传10或20
      "insure_pay_type" : 4,  // 保险缴费类型/频次 1年缴 4月缴   传1或4
      "insure_list" : [1], // 保险方案  1,2,3,4,5   1是主险为必选
      "insure_pay_amount" : 0 // 本期保险缴费金额  后端会单独计算并和前端比较
  },
  "user_list" : [
      {
        "index": 1,
          "user_type":1,
          "relation": 0,
          "user_name": "",
          "mobile": "",
          "id_type" : 1,
          "id_code" : "",
          "id_val_date" : "",
          "id_exp_date" : "",
          "occupation_type" : "",
          "contact_info" : {
              "address_type" : 1,
              "region_code" : "",
              "address": ""
          },
          
          // 保额超过50万触发反洗钱时必传的影像资料,
          // 若投、被保人为同一人，仅投保人提供， 否则 投保人和被保人都需要提供
          "image_list" : [ 
              {
                  "image_type": 1, // 1-身份证 7-户口簿 8-关系证明
                  //"relation_prove_type": "", // 类型是关系证明时，这里需要说明关系证明类型  1-户口本，2-结婚证、3-出生证，4-亲子鉴定 、5-其它法律部门开的证明
                  "img_url_1" : "",  // 身份证时：正面影像  户口簿时：被保人页  关系证明时：按需传
                  "img_url_2" : ""   // 身份证时：反面影像  户口簿时：投保人页  关系证明时：按需传
                  //"img_url_3" : "" // 身份证时：空       户口簿时：户主页    关系证明时：按需传
              }
          ],
      },
  ],
  "account_number": "", // 缴费银行帐户账号
  "account_name" : "",				// 缴费银行帐户户名
  "bank_number" : "",		// 缴费银行代码
}

export default function insure(state = INITIAL_STATE, action: { type: string; value: any }) {
  switch (action.type) {
    case SET_INSURE_DETAIL:
      return {
        ...state,
        ...action.value
      }
    case RESET_INSURE_DETAIL:
      return INITIAL_STATE;
    default:
      return state
  }
}
