import React from "react";
import pic1 from '~@/assets/computer/pic1.png'
import warnIcon from '~@/assets/computer/warnIcon.png'
import './index.scss'

const CalcResult = () => {

  return (
    <div className="calc-result-page">
      <div className="result">
        <div>预计退休后可领养老金（元/月）<span className="icon"></span></div>
        <div><span>¥</span><span>2563.64</span></div>
        <div>
          <div>
            <div>28</div>
            <div>距离退休（年）</div>
          </div>
          <div>
            <div>8</div>
            <div>还需缴费（年）</div>
          </div>
        </div>
      </div>
      <div className="result-tip">计算结果为估算 仅供参考</div>
      <div className="desc">
        <div className="desc-text">
          <p>经计算，您未来的养老金“只够温饱”</p>
          <p>养老金“只够温饱”</p>
          <p>无法满足退休后</p>
          <p>品质生活和就医需求</p>
        </div>
        <div className="desc-img">
          <img src={pic1} width={210} height={210} alt="" />
        </div>
        <div className="desc-float-icon-left">
          <img src={warnIcon}" />
        </div>
      </div>
      <div className="introduce">
        <div>为避免退休后“老无所依”</div>
        <div>立即补充官方推出的专属养老金</div>
        <div>
          2025年7月22日，由中国退役军人关爱基金会及中国人保会同15家银行等单位共同举办的“退役军人及家庭专属养老金项目暨公益捐赠启动仪式”在京举行，“退役军人家庭专属养老金”正式上线!
        </div>
      </div>
      <div className="video-box">
        <video poster="https://huijun-mobile-cdn.huijun365.com/video/cctv7.jpg" src='https://huijun-mobile-cdn.huijun365.com/video/cctv7.mp4'></video>
      </div>
      <div className="consult">
        <div>
          <img src='' />
          <span>养老规划疑问？立即咨询顾问！</span>
        </div>
        <div>
          <button>去咨询</button>
        </div>
      </div>
      <div className="goto">
        <button>点击获取官方专属养老金</button>
      </div>
    </div>
  )
}

export default CalcResult;