.calc-result-page {
  background: linear-gradient(180deg, rgba(39, 93, 245, 0.5), rgba(167, 205, 254, 0.5));
  min-height: 100vh;
  padding: 12px;
  box-sizing: border-box;
  overflow: auto;

  .page-wrap {
    background: white;
    margin: 0 auto;
    border-radius: 10px;

    // 结果展示区域
    .result {
      border-radius: 16px;
      padding: 12px;
      color: #3A4168;
      text-align: center;

      .title {
        font-size: 14px;
        margin-bottom: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        color: #3A4168;

        .icon {
          width: 41px;
          height: 31px;
          background: url('~@/assets/computer/pig.png');
          background-repeat: no-repeat;
          background-size: cover;
          display: inline-block;
        }
      }

      .number {
        font-size: 32px;
        font-weight: bold;
        margin-bottom: 20px;
        color: #18225C;

        span:first-child {
          font-size: 20px;
          margin-right: 4px;
        }
      }

      >div:last-child {
        display: flex;
        justify-content: space-around;
        gap: 20px;

        >div {
          flex: 1;
          text-align: center;

          >div:first-child {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
          }

          >div:last-child {
            font-size: 12px;
            opacity: 0.9;
          }
        }
      }
    }

    // 提示文字
    .result-tip {
      text-align: center;
      font-size: 12px;
      color: #999;
    }

    // 描述区域
    .desc {
      margin: 8px 20px;
      display: flex;
      justify-content: space-between;
      position: relative;
      background: #EFEFEF;
      border-radius: 16px;
      padding: 16px;
      margin-bottom: 20px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      align-items: center;

      .desc-text {
        width: 155px;
        height: 88px;
        font-size: 13px;
        color: #825750;
        line-height: 25px;
        text-align: justify;
      }

      .desc-img {

        img {
          width: 105px;
          height: 105px;
          display: block;
          border-radius: 8px;
        }
      }

      .desc-float-icon-left {
        position: absolute;
        top: -8px;
        left: -8px;

        img {
          width: 30px;
          height: 30px;
          display: block;
        }
      }
    }

    // 介绍区域
    .introduce {
      background: #fff;
      border-radius: 16px;
      padding: 0 20px;

      >div:first-child {
        font-size: 16px;
        color: #18225C;
        margin-bottom: 12px;
        text-align: center;
      }

      >div:nth-child(2) {
        font-size: 16px;
        color: #18225C;
        margin-bottom: 16px;
        text-align: center;
      }

      >div:last-child {
        font-size: 13px;
        line-height: 1.6;
        color: #666;
        text-align: justify;
      }
    }

    // 视频区域
    .video-box {
      margin: 20px;

      video {
        width: 100%;
        height: auto;
        display: block;
      }
    }

    // 咨询区域
    .consult {
      background: #fff;
      border-radius: 16px;
      padding: 20px;
      margin-bottom: 16px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      display: flex;
      align-items: center;
      justify-content: space-between;

      >div:first-child {
        display: flex;
        align-items: center;
        gap: 12px;

        img {
          width: 25px;
          height: 25px;
          border-radius: 50%;
        }

        span {
          font-size: 12px;
          color: #18225C;
        }
      }

      >div:last-child {
        button {
          font-size: 12px;
          color: #825750;
          border: none;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
          }

          &:active {
            transform: translateY(0);
          }
        }
      }
    }
  }

  // 底部按钮
  .goto {
    text-align: center;
    padding-bottom: 20px;

    button {
      width: 288px;
      height: 36px;
      background: linear-gradient(-28deg, #4976F3, #4990F7);
      color: white;
      border: none;
      border-radius: 24px;
      font-size: 16px;
      font-weight: bold;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
      }

      &:active {
        transform: translateY(0);
        box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
      }
    }
  }
}