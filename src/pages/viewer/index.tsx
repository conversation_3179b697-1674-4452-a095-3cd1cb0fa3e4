/*
 * @Author: your name
 * @Date: 2022-03-08 15:58:35
 * @LastEditTime: 2024-07-12 11:05:05
 * @LastEditors: wuqiang
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: /surpath-dray-saas/Users/<USER>/workspace/huijun-frontend/src/pages/activation/exemption/index.tsx
 */
import React, { useEffect } from 'react'
import { useLocation } from 'react-router-dom';

import Pdfh5 from 'pdfh5'
import 'pdfh5/css/pdfh5.css'
import { useDispatch, useSelector } from 'react-redux';
import { SET_INSURE_INFO } from '@/constants';
interface Params {
  name: string
}

const ActivateViewer = () => {
  const dispatch = useDispatch();
  const insureInfo = useSelector((state: any) => state.insureInfo);
  const useQuery =  () => {
    return new URLSearchParams(useLocation().search);
  }
  const path =  useQuery().get('path')
  // const params = useParams() 
  useEffect(() => {
    document.title = '投保协议'
    const pdfh5 = new Pdfh5('#activate-viewer', {
      pdfurl: path?.startsWith('http') ? path : `/pdf/${path}.pdf`
    })    
    //监听完成事件
    pdfh5.on('complete', function (status, msg, time) {
      console.log(
        '状态：' + status + '，信息：' + msg + '，耗时：' + time + '毫秒，总页数：'
      )
    })

  }, [])

  return (
    <>
      <section id="activate-viewer"></section>
    </>
  )
}

export default ActivateViewer
