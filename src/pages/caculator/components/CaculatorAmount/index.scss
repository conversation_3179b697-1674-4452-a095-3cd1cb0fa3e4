.caculator_amount {
  position: fixed;
  display: flex;
  flex-direction: column;
  bottom: 0;
  left: 0;
  width: 375px;
  height: 206.1px;
  background: url('../../../../assets/caculator_custom_modal.png') 50% 50% / cover no-repeat;
  h2 {
    height: 56px;
    line-height: 56px;
    font-weight: 400;
    box-sizing: border-box;
    padding-left: 20px;
    font-size: 15px;
    color: #7A2815;
    line-height: 55px;
    font-weight: 500;
  }
  .caculator_custom_row {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 30px auto;
    .caculator_custom_input {
      width: 297px;
      height: 36px;
      background: #FAEED3;
      border-radius: 4px;
      border: 1px solid #FFA72A;
      input {
        width: 297px;
        height: 36px;
        border: 0 none;
        line-height: 36px;
        box-sizing: border-box;
        padding: 0 10px;
      }
    }
    .caculator_custom_label {
      font-size: 14px;
      line-height: 28px;
      color: #7A2815;
      margin-left: 10px;
    }
  }
  .btn_group {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    .btn_ok {
      width: 160px;
      height: 40px;
      background: url('../../../../assets/caculator_custom_ok.png') 50% 50% / cover no-repeat;
      margin: 0 5px;
    }
    .btn_cancel {
      width: 160px;
      height: 40px;
      background: url('../../../../assets/caculator_custom_cancel.png') 50% 50% / cover no-repeat;
      margin: 0 5px;
    }
  }
}