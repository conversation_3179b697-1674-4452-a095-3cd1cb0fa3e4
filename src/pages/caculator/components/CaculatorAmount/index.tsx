/*
 * @Author: w<PERSON><PERSON><PERSON>
 * @Date: 2024-07-26 09:37:59
 * @LastEditors: wuqiang
 * @LastEditTime: 2024-07-26 16:41:30
 */
import { Toast } from 'antd-mobile';
import React, { useState } from 'react';
import './index.scss';

export function CaculatorAmount(props) {
  const {setIsShowCustomAmout, dispatchData, plan} = props;
  const [amount, setAmount] = useState(plan?.insure_amount_n || 0);
  
  return <div className='caculator_amount'>
    <h2>请输入金额：1万~75万元</h2>
    <div className='caculator_custom_row'>
      <div className='caculator_custom_input'>
        <input type='number' value={amount} onChange={e => {
          setAmount(Number(e.target.value?.trim()) || 0)
        }} />
      </div>
      <div className='caculator_custom_label'>万元</div>
    </div>
    <div className='btn_group'>
      <div className='btn_ok' onClick={() => {
        console.log('amount =>', amount)
        if (amount >= 1 && amount <= 75) {
          dispatchData('plan', { insure_amount_n: amount })
          setIsShowCustomAmout(false);
          
        } else {
          Toast.info('只能输入1-75之间的整数', 2, undefined,false)
        }
      }}></div>
      <div className='btn_cancel' onClick={() => {
        setIsShowCustomAmout(false);
      }}></div>
    </div>
  </div>
}