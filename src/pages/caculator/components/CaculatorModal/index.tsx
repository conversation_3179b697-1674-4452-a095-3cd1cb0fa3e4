/*
 * @Author: w<PERSON><PERSON><PERSON>
 * @Date: 2024-07-26 09:37:59
 * @LastEditors: wuqiang
 * @LastEditTime: 2024-07-27 16:21:39
 */
import React from 'react'
import './index.scss'
import caculator_result_btn from '@/assets/caculator_result_btn.png'
import caculator_arrow from '@/assets/caculator_arrow.png'
import { useHistory } from 'react-router-dom';
import { getAgeByBirthDay } from '@/utils';

export function CaculatorModal(props) {
  const { setIsShowCaculatorResult, plan, insured } = props;
  const history = useHistory()
  
  return (
    <div className="caculator_modal_mask" onClick={() => {
      setIsShowCaculatorResult(false);
    }}>
      <div className="caculator_modal" onClick={(e) => {
        e.stopPropagation()
      }}>
        <h2>测算结果</h2>
        <div className="caculator_result_row">姓 名：{insured?.user_name}</div>
        <div className="caculator_result_row">年 龄：{getAgeByBirthDay(plan?.brith_day)}</div>
        <div className="caculator_result_row">性 别：{plan?.gender === 'M' ? '男' : '女'}</div>
        <div className="caculator_result_row">您每月仅需<strong>{plan?.insure_pay_amount}</strong>元</div>
        <div className="caculator_result_row">就可以终身拥有<strong>{plan?.insure_amount_n}万</strong>补偿金</div>
        <div className="caculator_result_desc">
          因患病无法工作，导致无收入来源，影响各项开支。
          <br />
          有了这笔补偿金由您<strong>自由支配</strong>:<br />
          如（重大疾病的治疗费，孩子教育费，老人赡养费，房贷， 车贷等）
          <img src={caculator_arrow} className="arrow_left" alt="" />
          <img src={caculator_arrow} className="arrow_right" alt="" />
        </div>
        <img src={caculator_result_btn} className="caculator_result_btn" alt="" onClick={() => {
          history.push('/index')
        }} />
      </div>
    </div>
  )
}
