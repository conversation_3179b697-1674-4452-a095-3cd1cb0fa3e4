import React, { useEffect, useState } from 'react';
import qrcode from '@/assets/minenew/qrcode-img.png'
import foundation from '@/assets/minenew/foundation.png'
import picc from '@/assets/minenew/picc.png'
import beijing from '@/assets/minenew/beijing.png'
import tianjin from '@/assets/minenew/tianjin.png'
import chongqing from '@/assets/minenew/chongqing.png'
import zhejiang from '@/assets/minenew/zhejiang.png'
import hebei from '@/assets/minenew/hebei.png'
import shanxi from '@/assets/minenew/shanxi.png'
import anhui from '@/assets/minenew/anhui.png'
import guizhou from '@/assets/minenew/guizhou.png'
import yunnan from '@/assets/minenew/yunnan.png'
import hainan from '@/assets/minenew/hainan.png'
import jiangsu from '@/assets/minenew/jiangsu.png'
import guangdong from '@/assets/minenew/guangdong.png'
import arrowBottom from '@/assets/svg/arrow-bottom.svg'

import gongshangbank from '@/assets/minenew/gongshangbank.png'
import nongyebank from '@/assets/minenew/nongyebank.png'
import buildbank from '@/assets/minenew/buildbank.png'
import chinabank from '@/assets/minenew/chinabank.png'
import jiaotongbank from '@/assets/minenew/jiaotongbank.png'
import emsbank from '@/assets/minenew/emsbank.png'
import zhaoshangbank from '@/assets/minenew/zhaoshangbank.png'
import xingyebank from '@/assets/minenew/xingyebank.png'
import zhongxinbank from '@/assets/minenew/zhongxinbank.png'
import pufabank from '@/assets/minenew/pufabank.png'
import minshengbank from '@/assets/minenew/minshengbank.png'
import guangdabank from '@/assets/minenew/guangdabank.png'
import huaxiabank from '@/assets/minenew/huaxiabank.png'
import beijingbank from '@/assets/minenew/beijingbank.png'
import nanjingbank from '@/assets/minenew/nanjingbank.png'
import titleImg from '@/assets/minenew/title-img.jpg'
import { imgList2, imgList3 } from './img2';


import './index.scss'
import { getShareMaterial } from '@/utils/share';
import ShareGuide from '@/components/ShareGuide';
import clickReport from '@/utils/clickReport';

const BgPage = () => {
  const [hasMore, setHasMore] = useState(false)
  const [showModal, setShowModal] = useState(false)
  const [itemList, setItemList] = useState([
    {
      title: '专属保障一',
      text: '官方发起专属定制产品'
    },
    {
      title: '专属保障二',
      text: '税优加码精准适配人群'
    },
    {
      title: '专属保障三',
      text: '关爱老兵专属增值服务'
    },
    {
      title: '专属保障四',
      text: '绿色通道免除后顾之忧'
    },
    {
      title: '专属保障五',
      text: '尊享优待覆盖更多人群'
    },
    {
      title: '专属保障六',
      text: '专项保障见义勇为赠险'
    },
  ])

  const [videoList, setVideoList] = useState([
    {
      title: 'CCTV-1 《朝闻天下》',
      poster: 'https://huijun-mobile-cdn.huijun365.com/video/cctv1.jpg',
      url: 'https://huijun-mobile-cdn.huijun365.com/video/cctv1.mp4'

    },
    {
      title: 'CCTV-2 《正点财经》',
      poster: 'https://huijun-mobile-cdn.huijun365.com/video/cctv2.jpg',
      url: 'https://huijun-mobile-cdn.huijun365.com/video/cctv2.mp4'
    },
    {
      title: 'CCTV-7 《正午国防军事》',
      poster: 'https://huijun-mobile-cdn.huijun365.com/video/cctv7.jpg',
      url: 'https://huijun-mobile-cdn.huijun365.com/video/cctv7.mp4'
    },
    {
      title: 'CCTV-13 《新闻直播间》',
      poster: 'https://huijun-mobile-cdn.huijun365.com/video/cctv13.jpg',
      url: 'https://huijun-mobile-cdn.huijun365.com/video/cctv13.mp4'
    },
  ])

  const [bankList, setBankList] = useState(() => {
    return [
      {
        name: '中国工商银行',
        img: gongshangbank,
        width: 137,
        height: 38
      },
      {
        name: '中国农业银行',
        img: nongyebank,
        width: 153,
        height: 38
      },
      {
        name: '中国建设银行',
        img: buildbank,
        width: 120,
        height: 26
      },
      {
        name: '中国银行',
        img: chinabank,
        width: 119,
        height: 38
      },
      {
        name: '交通银行',
        img: jiaotongbank,
        width: 120,
        height: 30
      },
      {
        name: '中国邮政储蓄银行',
        img: emsbank,
        width: 155,
        height: 38
      },
      {
        name: '招商银行',
        img: zhaoshangbank,
        width: 118,
        height: 40
      },
      {
        name: '兴业银行',
        img: xingyebank,
        width: 130,
        height: 40
      },
      {
        name: '中信银行',
        img: zhongxinbank,
        width: 103,
        height: 29,
      },
      {
        name: '浦发银行',
        img: pufabank,
        width: 112,
        height: 32,
      },
      {
        name: '中国民生银行',
        img: minshengbank,
        width: 145,
        height: 30,
      },
      {
        name: '中国广大银行',
        img: guangdabank,
        width: 140,
        height: 21,
      },
      {
        name: '华夏银行',
        img: huaxiabank,
        width: 120,
        height: 40,
      },
      {
        name: '北京银行',
        img: beijingbank,
        width: 103,
        height: 24,
      },
      {
        name: '南京银行',
        img: nanjingbank,
        width: 125,
        height: 40,
      },
    ]
  })

  const clickMore = () => {
    setHasMore(!hasMore)
  }

  const sharePage = async () => {
    await clickReport(`老兵重疾保障背景分享-bgPage-分享按钮`);
  }

  useEffect(() => {
    getShareMaterial({
      url: 'bgPage',
      title: '官方发起的《退役军人家庭专属养老金》项目背景',
      text: '会同15家国内头部银行研发并开通税优通道，优化养老金储备方案。',
      imgUrl: 'https://cii.huijun365.com//head-img.png'
    })
  }, [])

  return (
    <div className="bg-page">
      <div className="bg-page-content">
        <div className='insurance-box'>
          <img width={145} height={28} src={foundation} alt="" />
          <img width={153} height={15} src={picc} alt="" />
        </div>
        <div className='head-img-box'>
          <img src={'/head-img.png'} alt="" />
        </div>
        <div className='item-box'>
          {
            itemList.map((it, ind) => {
              return (
                <div className='item' key={it.title + ind}>
                  <div className='item-title'>
                    {it.title}
                  </div>
                  <div className='item-content'>
                    {it.text}
                  </div>
                </div>
              )
            })
          }
        </div>

        <div className='register-box'>
          <div className='register-title'>养老金申办</div>
          <div className='register-subtitle'>为了您和家人拥有高品质养老生活</div>
          <div className='register-qrcode'>
            <img width={100} height={100} src={qrcode} alt="" />
            <div>长按二维码识别</div>
            <div>进行养老金申办与了解</div>
          </div>
        </div>

        <div className='project-box'>
          <div className='project-title'>项目背景</div>
          <div className='project-content'>
            <h2>退役军人事务部官网</h2>
            <h2>
              <img src={titleImg} />
            </h2>
            <p>
              为积极响应国家退役军人优待政策，切实解决退役军人家庭养老保障方面的实际需求，中国退役军人关爱基金会发起，央企金融机构中国人保会同15家银头部行研发并开通税优养老金通道，共同构建了退役军人家庭专属养老保障体系，携手推出全国首款《退役军人家庭专属养老金》
            </p>
            <p>
              旨在重点聚焦退役军人及其家庭养老为核心，专属养老金经过精心设计，力求贴合退役军人家庭特点与需求，提供更具针对性、可持续的养老解决方案。配套公益项目的同步启动，旨在为符合条件的困难退役军人提供帮扶与关爱，形成“养老+公益+服务"的创新模式。
            </p>
            <p>
              多方合力共同织密退役军人养老安全网，有效缓解其后顾之忧，切实提升退役军人的荣誉感、获得感和幸福感。
            </p>
          </div>
        </div>
        {
          videoList.map(item => {
            return (
              <div className='video-box' key={item.url}>
                <video className='player-video' poster={item.poster} src={item.url} controls={true} />
                <div className='text'>{item.title}</div>
              </div>
            )
          })
        }
        <div className='news-box'>
          <div className='news-title'>申请流程官方报道</div>
          <div className={`news-content${hasMore ? ' height-none' : ''}`}>
            {
              // foundationList.map((item, index) => {
              //   return (
              //     <div className='news-item' key={index}>
              //       <img className='item-img' src={item.img} width={30} height={30} />
              //       <div className='item-name'>{item.name}</div>
              //     </div>
              //   )
              // })
              imgList3.map((item, index) => {
                return (
                  <a href={item.url} className='news-item' key={item.name}>
                    <img className='item-img' src={item.img} width={62} height={62} />
                    <div className='item-name'>{item.name}</div>
                  </a>
                )
              })
            }
          </div>
          <div className='news-more' onClick={clickMore}>
            <span>{hasMore ? '收起' : '查看更多媒体报道'}</span>
            <img width={24} height={24} src={arrowBottom} style={{ transform: hasMore ? 'rotate(180deg)' : 'rotate(0deg)' }} />
          </div>
        </div>

        <div className='bank-box'>
          <div className='bank-title'>15家专属银行渠道</div>
          <div className='bank-content'>
            {
              bankList.map((item, index) => {
                return (
                  <img key={index} src={item.img} width={item.width / 2} height={item.height / 2} />
                )
              })
            }
          </div>
        </div>

        <div className='share-box' onClick={() => { setShowModal(true) }}>
          <button onClick={sharePage}>分享给战友</button>
        </div>
      </div>
      <ShareGuide isShow={showModal} setIsShow={setShowModal}></ShareGuide>
    </div>
  );
}

export default BgPage;