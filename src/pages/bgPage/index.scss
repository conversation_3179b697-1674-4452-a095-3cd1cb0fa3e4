.bg-page {
  .bg-page-content {
    background-image: url('~@/assets/minenew/page-bg.png');
    background-repeat: no-repeat;
    background-size: cover;

    .insurance-box {
      padding: 20px 15px;
      display: flex;
      justify-content: center;
      align-items: center;

      img {
        &:nth-child(1) {
          margin: 0 8px 0 0;
        }
      }
    }

    .head-img-box {
      img {
        width: 100%;
        height: 334px;
      }
    }

    .item-box {
      margin: 20px 0 0 0;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-wrap: wrap;
      gap: 10px;

      .item {
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;

        padding: 8px 0 0 0;
        width: 168px;
        height: 38px;
        background-image: url('~@/assets/minenew/yellowitem.png');
        background-repeat: no-repeat;
        background-size: contain;

        font-family: 'MiSans', sans-serif;
        font-weight: bold;
        font-size: 12px;
        color: #A54611;

        .item-title {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 90px;
          height: 20px;
          position: absolute;
          top: -10px;
          left: 10px;
          color: white;
          background: linear-gradient(to right, #FFD085, #E98400);
          border-radius: 8px;
        }
      }
    }

    .register-box {
      margin: 40px auto;
      width: 345px;
      height: 308px;
      background-color: #fff;
      box-shadow: 1px 4px 20px 2px #E48100;
      border-radius: 8px;

      display: flex;
      align-items: center;
      flex-direction: column;


      .register-title {
        margin-top: -20px;
        width: 302px;
        height: 62px;
        color: #fff;
        display: flex;
        justify-content: center;
        align-items: center;
        background: url('~@/assets/minenew/title-bg.png') 100% 100% / contain no-repeat;

        font-family: 'MiSans';
        font-weight: bold;
        font-size: 20px;
        color: #fff;
      }

      .register-subtitle {
        margin: 10px 0 0 0;
        font-family: 'MiSans';
        font-weight: bold;
        font-size: 14px;
        color: #252525;
        background-color: #f6b37f33; //#f6b37f
        line-height: 36px;
        width: 314px;
        height: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .register-qrcode {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        color: #252525;
        font-size: 12px;
        font-weight: 600;

        img {
          margin: 8px 0;
          width: 130px;
          height: 130px;
        }

        &>div {
          line-height: 20px;
        }
      }
    }

    .project-box {
      margin: 40px auto 20px auto;
      width: 345px;
      background-color: #fff;
      box-shadow: 1px 4px 20px 2px #E48100;
      border-radius: 8px;

      display: flex;
      align-items: center;
      flex-direction: column;

      .project-title {
        margin-top: -20px;
        width: 302px;
        height: 62px;
        color: #fff;
        display: flex;
        justify-content: center;
        align-items: center;
        background: url('~@/assets/minenew/title-bg.png') 100% 100% / contain no-repeat;

        font-family: 'MiSans';
        font-weight: bold;
        font-size: 20px;
        color: #fff;
      }

      .project-content {
        background-color: #f6b37f33; //#f6b37f
        margin: 16px;
        padding: 26px 16px;

        h2 {
          display: flex;
          justify-content: center;
          align-items: center;
          margin: 6px 0;
        }

        img {
          width: 100%;
          object-fit: contain;
        }

        p {
          text-align: justify;
          text-indent: 24px;
          line-height: 24px;
          font-size: 13px;
          font-weight: 600;
        }
      }
    }

    .video-box {
      margin: 0px auto 16px auto;
      width: 345px;
      background-color: transparent;
      box-shadow: 1px 4px 20px 2px #E48100;
      border-radius: 8px;

      display: flex;
      align-items: center;
      flex-direction: column;

      .player-video {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        border-radius: 8px;
      }

      .text {
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 16px;
        font-size: 16px;
        font-weight: 600;
        line-height: 24px;
      }

    }

    .news-box {
      margin: 40px auto 20px auto;
      width: 345px;
      background-color: #fff;
      box-shadow: 1px 4px 20px 2px #E48100;
      border-radius: 8px;

      display: flex;
      align-items: center;
      flex-direction: column;

      .news-title {
        margin-top: -20px;
        width: 302px;
        height: 62px;
        color: #fff;
        display: flex;
        justify-content: center;
        align-items: center;
        background: url('~@/assets/minenew/title-bg.png') 100% 100% / contain no-repeat;

        font-family: 'MiSans';
        font-weight: bold;
        font-size: 20px;
        color: #fff;
      }

      .news-content {
        &.height-none {
          height: auto;
        }

        margin: 20px 10px;
        display: flex;
        flex-wrap: wrap;
        width: 100%;
        height: 340px;
        overflow: hidden;

        .news-item {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          flex-direction: column;
          width: 25%;
          padding: 5px 0;

          .item-img {
            flex: 0 0 auto;
            width: 62px;
            height: 62px;
            border-radius: 50%;
          }

          .item-name {
            margin: 6px 0 0 0;
            width: 84px;
            transform: scale(0.8);
            // display: flex;
            // justify-content: center;
            // align-items: center;
            // flex-wrap: wrap;
            text-align: center;
            font-size: 12px;
            color: #2f2f2f;
            line-height: 18px;
          }
        }
      }

      .news-more {
        margin: 10px 0 20px 0;
        font-weight: 400;
        font-size: 12px;
        color: #2F2F2F;
        display: flex;
        align-items: center;
      }
    }

    .bank-box {

      margin: 40px auto 20px auto;
      width: 345px;
      background-color: #fff;
      box-shadow: 1px 4px 20px 2px #E48100;
      border-radius: 8px;

      display: flex;
      align-items: center;
      flex-direction: column;

      .bank-title {
        margin-top: -20px;
        width: 302px;
        height: 62px;
        color: #fff;
        display: flex;
        justify-content: center;
        align-items: center;
        background: url('~@/assets/minenew/title-bg.png') 100% 100% / contain no-repeat;

        font-family: 'MiSans';
        font-weight: bold;
        font-size: 20px;
        color: #fff;
      }

      .bank-content {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-wrap: wrap;
        gap: 8px;
        margin: 16px;
        padding: 12px;
        background-color: #f6b37f33;
      }
    }

  }

  .share-box {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 16px 0 32px 0;
    width: 100%;
    height: 100px;

    button {
      width: 245px;
      height: 69px;
      background-image: url('~@/assets/minenew/share-button.png');
      background-size: contain;
      display: flex;
      justify-content: center;
      align-items: flex-start;
      padding: 6px 0 0 0;
      font-family: 'MiSans', sans-serif;
      font-weight: bold;
      font-size: 22px;
      color: #A54611;
      line-height: 35px;

      animation: bian 0.5s ease-in-out infinite alternate;
    }
  }

}

@keyframes bian {
  0% {
    transform: scale(1);
  }

  100% {
    transform: scale(0.8);
  }
}