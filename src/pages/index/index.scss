.index-page {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  text-align: center;
  .body {
    width: 100%;
    flex: 1;
    overflow: auto;
    .image_row {
      width: 100%;
      border-bottom: 1px solid rgba(216, 216, 216, 1);
    }
    .image_list {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      margin: 10px auto;
      .image_item {
        margin-right: 10px;
      }
      .image {
        display: table-cell;
        position: relative;
        width: 100px;
        height: 100px;
        border: 1px solid #eee;
        vertical-align: middle;
        text-align: center;
        input.input_upload {
          position: absolute;
          left: 0;
          top: 0;
          right: 0;
          bottom: 0;
          opacity: 0;
          width: 100px !important;
          height: 100px !important;
        }
        img.upload {
          width: 60px;
          height: auto;
        }
        img.photo {
          max-width: 100%;
          height: auto;
        }
      }
      .image_desc {
        font-size: 14px;
        margin-top: 10px;
      }
    }
    input {
      position: absolute;
      left: 0;
      top: 0;
      right: 0;
      bottom: 0;
      width: 100%;
      height: 48px !important;
      line-height: 48px !important;
    }
    .table_card {
      background: rgba(245, 245, 245, 1) url('../../assets/index_header.png') 50% 0 / contain no-repeat;
      width: 100%;
      border-radius: 10px;
      background-color: rgba(245, 245, 245, 1);
      padding: 15px;
      padding-top: 430px;
      box-sizing: border-box;
      margin: 5px auto;
      .table_inner_card {
        width: 100%;
        border-radius: 10px;
        // background-color: #fff;
        // padding: 15px;
        box-sizing: border-box;
        margin-bottom: 5px;
        h2 {
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-size: 16px;
          small {
            font-size: 14px;
            color: red;
          }
        }
        table {
          width: 100%;
          border-collapse: collapse;
          margin: 15px auto;
          border: 1px solid #ecd7b0;
          td.table_title {
            background: #fffaf1;
            color: #786345;
          }
          td {
            font-size: 12px;
            padding: 10px;
            border: 1px solid #ecd7b0;
            text-align: left;
          }
        }
        .table_desc {
          display: flex;
          flex-direction: column;
          align-items: center;
          color: #999;
          font-size: 12px;
          margin: 10px auto;
          span, strong {
            margin: 3px auto;
          }
        }
        .table_row {
          width: 100%;
          display: flex;
          align-items: flex-start;
          justify-content: space-between;
          color: #999;
          font-size: 12px;
          margin: 10px auto;
          strong {
            color: #333;
            width: 100px;
            text-align: left;
          }
          span {
            flex: 1;
            text-align: right;
          }
        }
      }
      
    }
    .card {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 350px;
      border-radius: 10px;
      padding: 15px;
      box-sizing: border-box;
      background: #fff;
      margin: 10px auto;
      .title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        text-align: left;
        font-size: 16px;
        padding-bottom: 15px;
        margin-bottom: 15px;
        border-bottom: 1px solid rgba(216, 216, 216, 1);
      }
    }
    .index_example, .index_upgrade, .index_insurance, .index_reinsurance, .index_feature {
      width: 100%;
      margin: 5px auto;
    }
    .index_feature_1, .index_feature_2, .index_feature_3 {
      width: 100%;
      font-size: 0;

    }
    .question {
      width: 100%;
      padding-bottom: 15px;
      margin-bottom: 15px;
      
      border-bottom: 1px solid rgba(216, 216, 216, 1);
      &:last-of-type {
        border-bottom: 0 none;
        padding-bottom: 0;
        margin-bottom: 0;
      }
      h4 {
        width: 100%;
        display: flex;
        align-items: center;
        font-size: 14px;
        line-height: 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        & > span {
          display: flex;
          align-items: center;
          
        }
        img {
          width: 19px;
          height: 20.5px;
          margin-right: 5px;
        }
        img.arrow {
          width: 16px;
          height: 16px;
          margin-left: 10px;
        }
      }
      p {
        margin-top: 7px;
        font-size: 13px;
        line-height: 20px;
        color: #121212;
        text-align: left;
      }
    }
    .desc {
      font-size: 13px;
      line-height: 20px;
      text-align: left;
      color: #8F8F8F;
      margin-bottom: 10px;
    }
    .index_logo {
      width: 170px;
      height: 16px;
    }
    .applicant_date, .insured_date, .benefit_date {
      width: 100%;
      display: flex;
      align-items: center;
      // padding-top: 14px;
      // padding-bottom: 14px;
      height: 32px;
      line-height: 32px;
      border-bottom: 1px solid #d8d8d8;
      font-size: 14px;
      .split {
        width: 60px;
        font-size: 14px;
      }
      .id_long_date {
        font-size: 14px;
        color: red;
        margin-left: 15px;
      }
    }
    .insured_row {
      width: 320px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      // padding-top: 14px;
      // padding-bottom: 14px;
      height: 48px;
      line-height: 48px;
      border-bottom: 1px solid #d8d8d8;
      font-size: 14px;
      .insured_prefix {
        width: 75px;
        text-align: left;
        font-weight: 500;
      }
      .insured_center {
        position: relative;
        text-align: left;
        height: 100%;
        height: 16px;
        align-self: stretch;
        flex: 1;
        input {
          position: absolute;
          left: 0;
          top: 0;
          height: 100%;
          right: 0;
          bottom: 0;
        }
      }
      img.arrow {
        width: 16px;
        height: 16px;
        margin-left: 10px;
      }
    }
    .applicant_row {
      width: 100%;
      width: 320px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      // padding-top: 14px;
      // padding-bottom: 14px;
      height: 48px;
      line-height: 48px;
      border-bottom: 1px solid #d8d8d8;
      font-size: 14px;
      .applicant_prefix {
        width: 75px;
        text-align: left;
        font-weight: 500;
      }
      .applicant_center {
        position: relative;
        text-align: left;
        height: 100%;
        height: 18px;
        align-self: stretch;
        flex: 1;
        input {
          position: absolute;
          left: 0;
          top: 0;
          height: 100%;
          right: 0;
          bottom: 0;
        }
      }
      img.arrow {
        width: 16px;
        height: 16px;
        margin-left: 10px;
      }
    }
    .programme_row {
      width: 320px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-bottom: 14px;
      padding-top: 14px;
      border-bottom: 1px solid #d8d8d8;
      font-size: 14px;
      .arrow {
        width: 16px;
        height: 16px;
      }
    }
    .programme_responsibility {
      position: relative;
      width: 329px;
      height: 65px;
      padding: 14px 14px 14px 20px;
      box-sizing: border-box;
      background: #F6F6F6;
      border-radius: 9px;
      margin-top: 10px;
      label {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 62px;
        height: 18.5px;
        position: absolute;
        border-top-right-radius: 9px;
        border-bottom-left-radius: 9px;
        font-size: 12px;
        top: 0;
        right: 0;
        background: #D8D8D8;
        color: #fff;
      }
      h2 {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        width: 100%;
        font-size: 13px;
        img {
          width: 16px;
          height: 16px;
          margin-left: 5px;
        }
      }
      h4 {
        width: 100%;
        margin-top: 3px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        font-size: 12px;
        font-weight: 400;
        img {
          width: 16px;
          height: 16px;
        }
      }
      &.active {
        border: 1px solid #E32828;
        label {
          background-color: #E32828;
          color: #fff;
        }
      }
    }
    .introductions {
      width: 350px;
      display: flex;
      align-items: flex-start;
      margin: 20px auto 40px;
      .radio {
        position: relative;
        top: 3px;
        width: 14px;
        height: 14px;
        border-radius: 50%;
        border: 1px solid #8F8F8F;
        &.active {
          background: red;
          border: 1px solid yellow;
        }
      }
      .introduction {
        flex: 1;
        display: flex;
        align-items: flex-start;
        text-align: left;
        flex-wrap: wrap;
        font-size: 13px;
        color: #8F8F8F;
        line-height: 20px;
        margin-left: 10px;
        word-break: break-all;
        span {
          color: rgba(227, 40, 40, 1)
        }
      }
    }

    .beneficiary {
      width: 100%;
      display: flex;
      align-items: center;
    }
    .benefit {
      width: 100%;
      font-size: 14px;
      padding-bottom: 14px;

      h4 {
        width: 100%;
        font-size: 16px;
        color: #121212;
        text-align: left;
        padding-bottom: 14px;
        border-bottom: 1px solid #d8d8d8;
        display: flex;
        align-items: center;
        img {
          width: 18px;
          height: 18px;
          margin-left: 5px;
        }
      }
      .benefit_row {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        // padding-top: 14px;
        // padding-bottom: 14px;
        height: 48px;
        border-bottom: 1px solid #d8d8d8
      }
      .benefit_row_prefix {
        width: 110px;
        text-align: left;
        font-weight: bold;
        font-size: 14px;
      }
      .benefit_row_center {
        flex: 1;
        position: relative;
        text-align: left;
        display: flex;
        height: 100%;
        line-height: 48px;
      }
      img.arrow {
        width: 16px;
        height: 16px;
        margin-left: 10px;
      }
    }
    .benefit_add {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 120px;
      height: 30px;
      font-size: 14px;
      color:rgba(227, 40, 40, 1);
      border-radius: 6px;
      border: 1px solid #E32828;
      margin: 14px auto 0;
      img {
        width: 16px;
        height: 16px;
        margin-right: 5px;
      }
    }
    .btn {
      border: 1px solid rgba(143, 143, 143, 1);
      font-size: 14px;
      color: #8F8F8F;
      line-height: 20px;
      border-radius: 6px;
      padding: 4.5px 10px;
      box-sizing: border-box;
      margin: 0 4px;
      &.active {
        border: 1px solid rgba(227, 40, 40, 1);
        color: rgba(227, 40, 40, 1);
      }
    }
  }
  footer {
    width: 100%;
    height: 80px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    box-sizing: border-box;
    .service {
      display: flex;
      flex-direction: column;
      font-size: 12px;
      img {
        width: 36px;
        height: 36px;
      }
    }
    .desc {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      font-size: 16px;
      color: #565656;
      padding-left: 30px;
      strong {
        font-size: 18px;
        color: #E32828;
        margin-top: 5px;
      }
    }
    .index_btn {
      width: 172.5px;
      height: 70px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: url('../../assets/index_btn.png') 50% 50% / contain no-repeat;
      color: #fff;
      font-size: 21px;
    }
  }
}
