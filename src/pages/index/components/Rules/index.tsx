/*
 * @Author: w<PERSON><PERSON><PERSON>
 * @Date: 2024-07-10 14:36:34
 * @LastEditors: wuqi<PERSON>
 * @LastEditTime: 2024-07-18 15:37:30
 */
import React, { useEffect, useState } from 'react'
import Pdfh5 from 'pdfh5'
import 'pdfh5/css/pdfh5.css'
import './index.scss'
import { useSelector } from 'react-redux'
import request from '@/api/request'
import { Toast } from 'antd-mobile'

export function Rules(props) {
  const { setIsShowRules } = props
  const [step, setStep] = useState<number>(1)
  const [isLoading, setIsLoading] = useState(false);
  const [isReaded, setIsReaded] = useState(false);
  const insureInfo = useSelector((state: any) => state.insureInfo)

  const getPayUrl = async () => {
    const { data, error } = await request(`/insur_cii/api/policy/check_submit`, 'POST', {order_no: insureInfo?.order_no, is_intelligent: 0}, true);
    if (error) {
      Toast.info(error, 2, undefined, false);
    } else {
      if (!data) {
        Toast.info('投保接口未返回支付地址', 2, undefined, false);
      } else {
        window.location.href = data;
      }
    }
  }

  useEffect(() => {
    setIsLoading(true);
    setIsReaded(false);
    const pdfh5 = new Pdfh5('#activate-viewer', {
      pdfurl: `/pdf/step${step}.pdf`
    })    
    //监听完成事件
    pdfh5.on('complete', function (status, msg, time) {
      setIsLoading(false);
      let lastPage = document.querySelector('.pageContainer:last-of-type');
      if (lastPage && lastPage?.getBoundingClientRect()?.top < 500) {
        setIsReaded(true);
      }
      console.log(
        '状态：' + status + '，信息：' + msg + '，耗时：' + time + '毫秒，总页数：'
      )
    })

    
    let container = document.querySelector('.viewerContainer');

    container?.addEventListener('scroll', e => {
      let lastPage = document.querySelector('.pageContainer:last-of-type');
      console.log('test')
      console.log('top =>', lastPage?.getBoundingClientRect()?.top)
      if (lastPage && lastPage?.getBoundingClientRect()?.top < 500) {
        setIsReaded(true);
      }
    })
  }, [step])

  return (
    <div className="rules-modal">
      <div className="body" id='activate-viewer'></div>
      <div className="footer">
        {step < 4 ? (
          <span className={`btn ${isLoading ? 'disable' : ''}`} onClick={() => {
            !isLoading && setStep(step + 1);
          }}>我已完整阅读</span>
        ) : (
          <>
            <span id='有部分情况' className="cancel" onClick={() => {
               setIsShowRules(false);
               localStorage.setItem('order_no', insureInfo?.order_no)
               window.location.href = insureInfo?.intelligent_url;
            }}>有部分情况</span>
            <span id='以上情况全无' className="ok" onClick={() => {
              getPayUrl()
            }}>以上情况全无</span>
          </>
        )}
      </div>
    </div>
  )
}
