.rules-modal {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  display: flex;
  flex-direction: column;
  .body {
    flex: 1;
    overflow: auto;
    background-color: #f2f2f2;
  }
  .footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px;
    height: 80px;
    .btn,
    .ok {
      display: flex;
      align-items: center;
      justify-content: center;
      flex: 1;
      background: #fa5655;
      color: #fff;
      height: 48px;
      border-radius: 6px;
      &.disable {
        background-color: #f1f1f1;
        color: #666;
      }
    }
    .cancel {
      flex: 1;
      display: flex;
      height: 48px;
      align-items: center;
      justify-content: center;
      background-color: #f1f1f1;
      color: #666;
      margin-right: 10px;
      border-radius: 6px;
    }
  }
}
