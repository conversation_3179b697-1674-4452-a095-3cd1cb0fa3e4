/*
 * @Author: w<PERSON><PERSON><PERSON>
 * @Date: 2024-07-09 15:23:42
 * @LastEditors: wuqiang
 * @LastEditTime: 2024-07-17 01:09:35
 */
import React from 'react';
import index_example from '@/assets/index_example.png'
import index_table_1 from '@/assets/index_table_1.png'
import index_table_2 from '@/assets/index_table_2.png'
import index_table_3 from '@/assets/index_table_3.png'
import index_reinsurance from '@/assets/index_reinsurance.png'
import index_reinsurance_desc from '@/assets/index_reinsurance_desc.png'
import index_feature from '@/assets/index_feature.png'
import { useHistory } from 'react-router-dom';

export function Header() {
  const history = useHistory()
  return (
    <>
      <div className="table_inner_card">
        <img src={index_table_1} className='index_feature_1' onClick={() => {
                history.push('/viewer?path=disease')
              }} />
              <img src={index_table_2} className='index_feature_2' />
              <img src={index_table_3} className='index_feature_3' onClick={() => {
                history.push('/viewer?path=guarantee')
              }} />
            {/* <h2>
              <span>疾病保障责任</span>
              <small onClick={() => {
                history.push('/viewer?path=disease')
              }}>查看疾病详情 &gt;</small>
            </h2>
            <table>
              <tbody>
                <tr>
                  <td className="table_title">基本保额</td>
                  <td>最高75万元(可</td>
                </tr>
                <tr>
                  <td className="table_title">120种重大疾病保险金</td>
                  <td>赔付100%基本保额，一次为限</td>
                </tr>
                <tr>
                  <td className="table_title">20种中症疾病保险金</td>
                  <td>赔付60%基本保额，最多赔付3次</td>
                </tr>
                <tr>
                  <td className="table_title">40种轻症疾病保险金</td>
                  <td>赔付30%基本保额，最多赔付5次</td>
                </tr>
                <tr>
                  <td className="table_title">身故保险金</td>
                  <td>
                    未满18周岁返还已交保险费(不计利息)，已满18周岁赔付100%基本保额。注：赔付第一次重大疾病保险金时身故保险金责任终止
                  </td>
                </tr>
                <tr>
                  <td className="table_title">轻/中/重症豁免</td>
                  <td>免交确诊日之后的各期保费</td>
                </tr>
                <tr>
                  <td className="table_title">增值服务</td>
                  <td>8项增值服务</td>
                </tr>
                <tr>
                  <td className="table_title">保障期限</td>
                  <td>终身/年满70周岁后的首个保单年生效对应日零时止</td>
                </tr>
                <tr>
                  <td className="table_title">疾病关爱保险金(可选)</td>
                  <td>
                    60周岁后的首个保单年生效对应日零时前患病额外赔付，轻/中/重症分别赔付基本保额10%/30%/80%，各限一次
                  </td>
                </tr>
                <tr>
                  <td className="table_title">重大疾病扩展保险金(可选)</td>
                  <td>
                    非同种重疾赔付100%基本保额，一次为限，60周岁后的首个保单年生效对应日零时前，时间间隔365天
                  </td>
                </tr>
                <tr>
                  <td className="table_title">重度恶性肿瘤扩展保险金(可选)</td>
                  <td>赔付基本保额120%，一次为限，间隔期180天/3年</td>
                </tr>
                <tr>
                  <td className="table_title">特定心脑血管疾病扩展保险金(可选)</td>
                  <td>赔付基本保额120%，一次为限，间隔期180天/365天</td>
                </tr>
              </tbody>
            </table>
            <div className="table_desc">
            <strong style={{ color: 'red' }} onClick={() => {
                history.push('/viewer?path=guarantee')
              }}>点击查看专属保障 &gt;</strong>
              <span>产品备案名</span>
              <span>人保寿险i无忧2.0重大疾病保险(B款)</span>
              <span>(互联网专属)(人寿保险[2023]疾病保险051号)</span>
              
            </div> */}
           
          </div>
          <img src={index_feature} className="index_feature" alt="" />
          <img src={index_reinsurance} className="index_reinsurance" alt="" />
          <img src={index_reinsurance_desc} className="index_reinsurance" alt="" />
          {/* <img src={index_insurance} className="index_insurance" alt="" /> */}
         
          <img src={index_example} className="index_example" alt="" />
    </>
  )
}