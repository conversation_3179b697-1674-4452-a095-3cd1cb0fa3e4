/*
 * @Author: w<PERSON><PERSON><PERSON>
 * @Date: 2024-07-05 17:28:00
 * @LastEditors: wuqi<PERSON>
 * @LastEditTime: 2024-07-10 14:51:07
 */
import { SET_INSURE_INFO } from '@/constants';
import React, { useState } from 'react'
import { useDispatch, useSelector } from 'react-redux';
import './index.scss'

export function EstimateModal(props) {
  const { isShowEstimate, setIsShowEstimate, setIsShowRules } = props;
  const dispatch = useDispatch();
  const insureInfo = useSelector((state: any) => state.insureInfo)

  const [isShowResult, setIsShowResult] = useState(false);

  return (
    <div className="mask">
      <div className="estimate-modal">
        <h2>《适当性评估问卷》</h2>
        <h4>1、风险保障需求评估</h4>
        <p>
          您投保的产品为疾病保险，指发生保险合同约定的疾病时，为被保险人提供保障的保险。保险期间为终身。该保险是否符合被保险人的保障需求
        </p>
        <div className='estimate_btns'>
          <span className={`estimate_btn ${insureInfo.question1 === 1 ? 'active' : ''}`} onClick={() => {
            !isShowResult && dispatch({
              type: SET_INSURE_INFO,
              value: {
                question1: 1
              }
            })
          }}>是</span>
          <span className={`estimate_btn ${insureInfo.question1 === 0 ? 'active' : ''}`} onClick={() => {
            !isShowResult && dispatch({
              type: SET_INSURE_INFO,
              value: {
                question1: 0
              }
            })
          }}>否</span>
        </div>
        <h4>2、支付能力评估</h4>
        <p>
          本产品年交保费为元，交费期间为10年，交费期间内累计交纳保费95800元，保费是否超过家庭年收入的20%？
        </p>
        <div className='estimate_btns'>
          <span className={`estimate_btn ${insureInfo.question2 === 1 ? 'active' : ''}`} onClick={() => {
            !isShowResult && dispatch({
              type: SET_INSURE_INFO,
              value: {
                question2: 1
              }
            })
          }}>是</span>
          <span className={`estimate_btn ${insureInfo.question2 === 0 ? 'active' : ''}`} onClick={() => {
            !isShowResult && dispatch({
              type: SET_INSURE_INFO,
              value: {
                question2: 0
              }
            })
          }}>否</span>
        </div>

        <div className='estimate_ok' onClick={() => {
          if (insureInfo.question1 === 0 || insureInfo.question2 === 1) {
            setIsShowResult(true);
          } else {
            setIsShowEstimate(false);
            setIsShowRules(true);
          }
        }}>确定</div>
      </div>

      { isShowResult ? <div className='estimate-result'>
        <h2>评估结果</h2>
        <p>{insureInfo.question1 === 0 ? '该产品不符合被保险人的保障需求，建议你重新评估或投保其他产品' : '该产品交纳保费超过您家庭年收入的20%，您是否确认投保？'}</p>
        { insureInfo.question1 === 0 ? <div className='estimate-btns'>
          <div className='estimate-btn-ok' onClick={() => {
            setIsShowResult(false)
          }}>确认</div>
        </div> : <div className='estimate-btns'>
          <div className='estimate-btn-cancel' onClick={() => {
            setIsShowResult(false)
          }}>我再想想</div>
          <div className='estimate-btn-ok' onClick={() => {
            setIsShowResult(false)
            setIsShowEstimate(false);
            setIsShowRules(true);
          }}>确认投保</div>
        </div>}

      </div> : null}
    </div>
  )
}
