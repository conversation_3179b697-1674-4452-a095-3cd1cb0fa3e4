.mask {
  position: fixed;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  left: 0;
  top: 0;
  width: 100%;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  padding: 20px;
  .estimate-modal {
    background: #fff;
    border-radius: 8px;
    width: 350px;
    padding: 15px;
    box-sizing: border-box;
    h2 {
      width: 100%;
      text-align: center;
      font-size: 16px;
    }
    h4 {
      font-size: 13px;
      margin-top: 15px;
      text-align: left;
    }
    p {
      font-size: 12px;
      line-height: 1.5;
      text-align: left;
      margin-top: 5px;
    }
    .estimate_btns {
      width: 100%;
      display: flex;
      align-items: center;
      margin-top: 10px;
      .estimate_btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 150px;
        font-size: 14px;
        color: #666;
        line-height: 20px;
        border-radius: 6px;
        padding: 4.5px 10px;
        box-sizing: border-box;
        background-color: #f2f2f2;
        margin: 0 4px;
        &.active {
          background: #fef2f2;
          color: #fa5655;
        }
      }
    }

    .estimate_ok {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 15px;
      font-size: 14px;
      box-sizing: border-box;
      padding: 4.5px 10px;
      line-height: 30px;
      background: #fa5655;
      color: #fff;
      border-radius: 6px;
    }
  }
  .estimate-result {
    position: absolute;
    bottom: 20px;
    background: #fff;
    border-radius: 8px;
    width: 350px;
    padding: 15px;
    box-sizing: border-box;
    box-shadow: 4px -8px 8px 4px rgba(0,0,0,0.2), ;
    h2 {
      width: 100%;
      text-align: center;
      font-size: 16px;
    }
    p {
      font-size: 12px;
      line-height: 1.5;
      text-align: left;
      margin-top: 5px;
    }
    .estimate-btns {
      display: flex;
      align-items: center;
      .estimate-btn-ok, .estimate-btn-cancel {
        border-radius: 6px;
        margin: 5px;
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 15px;
        font-size: 14px;
        box-sizing: border-box;
        padding: 4.5px 10px;
        line-height: 30px;
        background: #fa5655;
        color: #fff;
      }
      .estimate-btn-cancel {
        background-color: #f1f1f1;
        color: #666
      }
    }
  }
}