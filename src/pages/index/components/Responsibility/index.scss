.mask {
  position: fixed;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  left: 0;
  top: 0;
  width: 100%;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  padding: 20px;
  .responsibility-modal {
    background: #fff;
    border-radius: 8px;
    width: 350px;
    padding: 15px;
    box-sizing: border-box;
    h2 {
      width: 100%;
      text-align: center;
      font-size: 16px;
      margin-bottom: 10px;
    }
    h4 {
      font-size: 13px;
      margin-top: 15px;
      text-align: left;
    }
    p {
      font-size: 12px;
      line-height: 1.5;
      text-align: left;
      margin-top: 5px;
    }
    .estimate_btns {
      width: 100%;
      display: flex;
      align-items: center;
      margin-top: 10px;
      .estimate_btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 150px;
        font-size: 14px;
        color: #666;
        line-height: 20px;
        border-radius: 6px;
        padding: 4.5px 10px;
        box-sizing: border-box;
        background-color: #f2f2f2;
        margin: 0 4px;
        &.active {
          background: #fef2f2;
          color: rgba(227, 40, 40, 1);
        }
      }
    }

    .estimate_ok {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 15px;
      font-size: 14px;
      box-sizing: border-box;
      padding: 4.5px 10px;
      line-height: 30px;
      background: rgba(227, 40, 40, 1);
      color: #fff;
    }
  }
}