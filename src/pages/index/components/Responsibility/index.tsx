/*
 * @Author: w<PERSON><PERSON><PERSON>
 * @Date: 2024-07-05 17:28:00
 * @LastEditors: wuqi<PERSON>
 * @LastEditTime: 2024-07-09 20:10:50
 */
import React from 'react'
import './index.scss'

const articles = {
  '1': {
    title: '疾病关爱保险金',
    content: `<p>
  （1）、轻症疾病关爱保险金<br/>
被保险人于等待期后，且于年满60周岁后的首个保单年生效对应日零时之前，经我们认可的医院确诊初次患有本合同约定的轻症疾病（一种或多种），且此前未确诊患有本合同约定的重大疾病（一种或多种），则我们在给付轻症疾病保险金的同时，按基本保险金额的10%给付轻症疾病关爱保险金。轻症疾病关爱保险金给付以一次为限。<br/>
（2）、中症疾病关爱保险金<br/>
被保险人于等待期后，且于年满60周岁后的首个保单年生效对应日零时之前，经我们认可的医院确诊初次患有本合同约定的中症疾病（一种或多种），且此前未确诊患有本合同约定的重大疾病（一种或多种），则我们在给付中症疾病保险金的同时，按基本保险金额的30%给付中症疾病关爱保险金。中症疾病关爱保险金给付以一次为限。<br/>
（3）、重大疾病关爱保险金<br/>
被保险人于等待期后，且于年满60周岁后的首个保单年生效对应日零时之前，经我们认可的医院确诊初次患有本合同约定的重大疾病（一种或多种），则我们在给付第一次重大疾病保险金的同时，按基本保险金额的80%给付重大疾病关爱保险金。重大疾病关爱保险金给付以一次为限。</p>`
  },
  '2': {
    title: '重大疾病扩展保险金',
    content: `<p>
    （1）、我们给付第一次重大疾病保险金后，被保险人于第一次重大疾病确诊之日起365日后，且于年满60周岁后的首个保单年生效对应日零时之前，经我们认可的医院确诊初次患有第一次重大疾病以外的本合同约定的其他重大疾病（一种或多种），我们按基本保险金额给付重大疾病扩展保险金，本项保险责任终止。<br/>
（2）、若被保险人于年满60周岁后的首个保单年生效对应日零时前未满足本合同约定的重大疾病扩展保险金的给付条件，本项保险责任终止。<br/>
（3）、重大疾病扩展保险金给付以一次为限。
    </p>`
  },
  '3': {
    title: '重度恶性肿瘤扩展保险金',
    content: `<p>（1）、我们给付第一次重大疾病保险金后，若被保险人第一次重大疾病为本合同约定的“恶性肿瘤——重度”，则自该“恶性肿瘤——重度”确诊之日起3年后，若被保险人处于“恶性肿瘤——重度”状态，我们按基本保险金额的120%给付重度恶性肿瘤扩展保险金，本项保险责任终止。<br/>
    （2）、我们给付第一次重大疾病保险金后，若被保险人第一次重大疾病为本合同约定的除“恶性肿瘤——重度”以外的重大疾病（一种或多种），则自该重大疾病确诊之日起180日后，若被保险人经我们认可的医院确诊初次患有本合同约定的“恶性肿瘤——重度”，我们按基本保险金额的120%给付重度恶性肿瘤扩展保险金，本项保险责任终止。<br/>
    （3）、重度恶性肿瘤扩展保险金给付一次为限。</p>`
  },
  '4': {
    title: '特定心脑血管疾病扩展保险金',
    content: `<p>
    （1）、我们给付第一次重大疾病保险金后，若被保险人第一次重大疾病为本合同约定的特定心脑血管疾病 （一种或多种），则自该特定心脑血管疾病确诊之日起365日后，被保险人经我们认可的医院确诊初次患有第一次重大疾病以外的本合同约定的其他特定心脑血管疾病（一种或多种），我们按基本保险金额的120%给付特定心脑血管疾病扩展保险金，本项保险责任终止。<br/>
（2）、我们给付第一次重大疾病保险金后，若被保险人第一次重大疾病为本合同约定的除特定心脑血管疾病以外的重大疾病（一种或多种），则自该重大疾病确诊之日起180日后，被保险人经我们认可的医院确诊初次患有本合同约定的特定心脑血管疾病（一种或多种），我们按基本保险金额的120%给付特定心脑血管疾病扩展保险金，本项保险责任终止。<br/>
（3）、特定心脑血管疾病扩展保险金给付以一次为限。
    </p>`
  }
}

export function Responsibility(props) {
  const { setIsShowResponsibility, responsibility } = props

  return (
    <div className="mask">
      <div className="responsibility-modal">
        <h2>{articles[responsibility]?.title}</h2>
     
       <div dangerouslySetInnerHTML={{__html: articles[responsibility]?.content}} />

        <div
          className="estimate_ok"
          onClick={() => {
            setIsShowResponsibility(0)
          }}>
          确定
        </div>
      </div>
    </div>
  )
}
