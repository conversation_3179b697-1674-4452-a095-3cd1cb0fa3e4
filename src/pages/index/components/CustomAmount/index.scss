.mask {
  position: fixed;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  left: 0;
  top: 0;
  width: 100%;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  padding: 20px;
  .custom-amount-modal {
    background: #fff;
    border-radius: 8px;
    width: 350px;
    padding: 15px;
    box-sizing: border-box;
    h2 {
      width: 100%;
      text-align: center;
      font-size: 16px;
      small {
        font-size: 12px;
        color: #666;
      }
    }

    .custom-amount-box {
      input {
        width: 100%;
        height: 36px;
        line-height: 36px;
        box-sizing: border-box;
        padding: 0 10px;
        margin: 10px 0;
        border: 1px solid #999;
      }
    }
   
    .custom-amount-erros {
      font-size: 12px;
      color: red;
      text-align: left;
    }
    
    .custom-amount-btns {
      width: 100%;
      display: flex;
      align-items: center;
      margin-top: 10px;
      span {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 150px;
        font-size: 14px;
        color: #666;
        line-height: 20px;
        border-radius: 6px;
        padding: 4.5px 10px;
        box-sizing: border-box;
        background-color: #f2f2f2;
        margin: 0 4px;
        &.active {
          background: #fef2f2;
          color: rgba(227, 40, 40, 1);
        }
      }
    }
  }
}