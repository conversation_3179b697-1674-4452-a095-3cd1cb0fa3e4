/*
 * @Author: w<PERSON><PERSON><PERSON>
 * @Date: 2024-07-05 17:28:00
 * @LastEditors: wuqiang
 * @LastEditTime: 2024-07-16 14:31:09
 */
import React, { useEffect, useState } from 'react'
import './index.scss'

export function CustomAmount(props) {
  const { dispatchData, amount,  setIsShowCustomAmount} = props;
  const [total, setTotal] = useState(amount);
  const [message, setMessage] = useState('')

  useEffect(() => {
    console.log('total =>', total);
    if(!/^[1-9]+[0]*0{4}$/.test(total)) {
      setMessage('保险金额必须为整数且为10000的倍数')
    } else if (
      total > 750000
    ) {
      setMessage('保险金额最高不得超过750000')
    } else {
      setMessage('')
    }
  }, [total])
  
  return (
    <div className="mask">
      <div className="custom-amount-modal">
        <h2>请输入保险金额<small>（最高设定750000）</small></h2>
        <div className='custom-amount-box'>
          <input value={total} onChange={e => {
            setTotal(e.target.value?.trim())
          }} />
        </div>
        <div className='custom-amount-erros'>
          {message}
        </div>
        <div className='custom-amount-btns'>
          <span className='active' onClick={() => {
            if (!message) {
              dispatchData('plan', { insure_amount_n: total / 10000 });
              setIsShowCustomAmount(false);
            }
          }}>确定</span>
          <span onClick={() => {
            setIsShowCustomAmount(false);
          }}>取消</span>
        </div>
      </div>
    </div>
  )
}
