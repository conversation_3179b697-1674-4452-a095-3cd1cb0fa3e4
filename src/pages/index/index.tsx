/*
 * @Author: w<PERSON><PERSON><PERSON>
 * @Date: 2023-08-08 09:15:28
 * @LastEditors: wuqiang
 * @LastEditTime: 2024-09-24 11:00:53
 */
import React, { useCallback, useEffect, useRef, useState } from 'react'
import { getShareMaterial } from '@/utils/share'
import dayjs from 'dayjs'
import './index.scss'
import index_logo from '@/assets/index_logo.png'
import ic_question from '@/assets/ic_question.png'
import minus from '@/assets/ic_minus.png'
import plus from '@/assets/ic_plus.png'
import arrow from '@/assets/ic_arrow_right.png'
import question from '@/assets/ic_question_outline.png'
import checked from '@/assets/ic_checked.png'
import unchecked from '@/assets/ic_unchecked.png'
import ic_upload from '@/assets/ic_upload.png'
import { useHistory } from 'react-router-dom'
import index_upgrade from '@/assets/index_upgrade.png'
import index_insurance from '@/assets/index_insurance.png'
import icon_service from '@/assets/icon_service.png'

import districtData from '@/constants/location.json'
import bankData from '@/constants/bank.json'
import occupationData from '@/constants/occupationData.json'
import policyData from '@/constants/policy_rates.json'
import { SET_INSURE_DETAIL, RESET_INSURE_DETAIL, SET_INSURE_INFO } from '@/constants/index'
import { EstimateModal } from './components/EstimateModal'
import { useDispatch, useSelector } from 'react-redux'
import { Picker, Calendar, DatePicker, Toast } from 'antd-mobile'
import { APPLICANT, BENEFIT } from '@/store/reducers/insure'
import {
  getAge,
  getAgeByBirthDay,
  getGender,
  isValidApplicantAge,
  isValidIdCard,
  isValidInsured,
  isValidName,
  isValidPhone,
  isValidSelfAge
} from '@/utils'
import { Header } from './components/Header'
import { CustomAmount } from './components/CustomAmount'
import { Responsibility } from './components/Responsibility'
import request from '@/api/request'
import { Rules } from './components/Rules'
import BlueCardEndModal from '@/components/BlueCardEndModal'
let antdOccupation: any[] = []
let antdDistrict: any[] = []
let antdDistrictLabels: any = {}
let addressTypes: any[] = [
  {
    value: 1,
    label: '现/常住地址'
  },
  {
    value: 27,
    label: '工作单位地址'
  },
  {
    value: 100,
    label: '户籍地址'
  }
]
let benefitOrders: any[] = [
  {
    value: 1,
    label: '1'
  },
  {
    value: 2,
    label: '2'
  },
  {
    value: 3,
    label: '3'
  },
  {
    value: 4,
    label: '4'
  },
  {
    value: 5,
    label: '5'
  }
]
let insuranceRate = {
  '1': 1.2,
  '2': 0.5,
  '3': 0.1,
  '4': 0.5,
  '5': 0.1
}

const now = new Date()

Object.keys(districtData).forEach(index => {
  let itemLevel1 = {} as any
  let itemLevel2 = {} as any
  itemLevel1.value = districtData[index].name
  itemLevel1.label = districtData[index].name
  itemLevel1.children = []
  let data = districtData[index].cities
  Object.keys(data).forEach(index => {
    itemLevel2.value = data[index].name
    itemLevel2.label = data[index].name
    itemLevel2.children = []
    let data2 = data[index].districts
    let itemLevel3 = {} as any
    itemLevel3.children = []
    Object.keys(data2).forEach(index => {
      itemLevel3.value = data2[index]
      itemLevel3.label = data2[index]
      itemLevel2.children.push(itemLevel3)
      antdDistrictLabels[`${itemLevel1.label}-${itemLevel2.label}-${itemLevel3.label}`] = index
      itemLevel3 = {}
    })
    itemLevel1.children.push(itemLevel2)
    itemLevel2 = {}
  })
  antdDistrict.push(itemLevel1)
})

console.log('antdDistrict =>', antdDistrict)

const traverseOccupation = (params: any) => {
  if (!params.value) {
    params.value = params.label
    if (params.id) delete params.id
    if (params.version) delete params.version
    if (params.begin) delete params.begin
    if (params.end) delete params.end
    if (params.children) {
      params.children.forEach(child => {
        traverseOccupation(child)
      })
    }
  } else {
    antdOccupation.push(params)
  }
}
occupationData.forEach((level1: any) => {
  traverseOccupation(level1)
})
console.log('occupationData =>', occupationData)

function Index() {
  const history = useHistory()
  const dispatch = useDispatch()
  const [isShowEstimate, setIsShowEstimate] = useState(false)
  const [isShowResponsibility, setIsShowResponsibility] = useState(0)
  const [isShowCustomAmount, setIsShowCustomAmount] = useState(false)
  const [isShowRules, setIsShowRules] = useState(false)
  const [payDuras, setPayDuras] = useState<number[]>([])
  const [termTypes, setTermTypes] = useState<number[]>([])
  const [totalInsurance, setTotalInsurance] = useState<number>(0)
  const [oss, setOss] = useState<any>(null)
  const [questions, setQuestions] = useState([
    {
      q: '这项保险的保障内容包括哪些？',
      a: '此项保障基础责任包括40种轻症疾病保险金，20种中症疾病保险金、120种第一次重大疾病保险金及身故保险金和豁免保险费。可选责任包括疾病关爱保险金、重大疾病扩展保险金、重度恶性肿瘤扩展保险金、特定心脑血管疾病扩展保险金，4种可选责任支持任意搭配。',
      show: false
    },
    { q: '多少岁可以办理', a: '本保障投保年龄为出生满28天-55周岁。', show: false },
    {
      q: '此项保障如何确定健康状况？',
      a: '本产品投保无需体检，您投保时需如实告知被保人情况，我司通过健康告知、智能核保结论以及公司系统内部规则进行自动核保。您在点击立即投保后，进入健康告知页，当您选择“有部分情况”时将会自动进入智能核保系统，您需按照被保人实际情况进行疾病选择。',
      show: false
    },
    {
      q: '这个保障是人保推出的吗？',
      a: '是的，此项保障是由中国人保寿险推出的重大疾病保险。',
      show: false
    }
  ])
  const insure = useSelector((state: any) => state.insure)
  const user = useSelector((state: any) => state.user)
  const insureInfo = useSelector((state: any) => state.insureInfo)
  const plan = useSelector((state: any) => state.insure?.insure_plan)
  const applicant = useSelector((state: any) =>
    state.insure?.user_list?.find((item: any) => item.user_type === 2)
  ) // 投保人
  const insured = useSelector((state: any) =>
    state.insure?.user_list?.find((item: any) => item.user_type === 1)
  ) // 被保人
  const beneficiary = useSelector((state: any) =>
    state.insure?.user_list?.filter((item: any) => item.user_type === 3)
  )
  const bank = useSelector((state: any) => ({
    account_number: state.insure?.account_number,
    account_name: state.insure?.account_name,
    bank_number: state.insure?.bank_number
  }))

  const insuredImg1Ref = useRef<any>()
  const insuredImg2Ref = useRef<any>()
  const insuredImg3Ref = useRef<any>()
  const applicantImg1Ref = useRef<any>()
  const applicantImg2Ref = useRef<any>()
  const applicantImg3Ref = useRef<any>()
  const introRef = useRef<any>()
  const insuredRef = useRef<any>()
  const applicantRef = useRef<any>()
  const benefitRef = useRef<any>()
  const planRef = useRef<any>()

  const dispatchData = (type: string, obj: Record<string, any>) => {
    let _user_list = JSON.parse(JSON.stringify(insure.user_list))
    switch (type) {
      case 'plan':
        dispatch({
          type: SET_INSURE_DETAIL,
          value: {
            insure_plan: {
              ...plan,
              ...obj
            }
          }
        })
        break
      case 'bank':
        dispatch({
          type: SET_INSURE_DETAIL,
          value: {
            ...obj
          }
        })
        break
      case 'insured':
        let _insured = JSON.parse(JSON.stringify(insured))
        _insured = { ..._insured, ...obj }
        console.log('_insured =>', _insured)
        let _index = insure.user_list.findIndex(item => item.user_type === 1)
        let _applicant_index_1 = insure.user_list.findIndex(item => item.user_type === 2)
        _user_list[_index] = _insured

        switch (obj.relation) {
          case 0:
            if (_applicant_index_1 >= 0) {
              _user_list.splice(_applicant_index_1, 1)
            }
            break
          case 1:
          case 2:
          case 3:
            if (_applicant_index_1 < 0) {
              _user_list.splice(1, 0, APPLICANT)
            }
        }
        console.log('_user_list =>', _user_list)
        dispatch({
          type: SET_INSURE_DETAIL,
          value: {
            user_list: _user_list
          }
        })
        break

      case 'applicant':
        let _applicant_index = insure.user_list.findIndex(item => item.user_type === 2)
        if (obj) {
          let _applicant = JSON.parse(JSON.stringify(applicant))
          _applicant = { ..._applicant, ...obj }

          _user_list[_applicant_index] = _applicant
          dispatch({
            type: SET_INSURE_DETAIL,
            value: {
              user_list: _user_list
            }
          })
        } else {
          _user_list.splice(_applicant_index, 1)
          dispatch({
            type: SET_INSURE_DETAIL,
            value: {
              user_list: _user_list
            }
          })
        }

        break
      case 'beneficiary':
        let _others = insure.user_list.filter(item => item.user_type !== 3)
        dispatch({
          type: SET_INSURE_DETAIL,
          value: {
            user_list: [..._others, ...(obj?.beneficiary || [])]
          }
        })
        break
    }
  }

  const handleBeneficiary = (index: number, obj: Record<string, any>) => {
    let _beneficiary = JSON.parse(JSON.stringify(beneficiary))
    let _index = _beneficiary.findIndex(item => item.index === index)
    _beneficiary[_index] = { ..._beneficiary[_index], ...obj }
    console.log('_beneficiary[_index] =>', _beneficiary[_index])
    dispatchData('beneficiary', {
      beneficiary: _beneficiary
    })
  }

  const handleClickPlan = (p: any) => {
    let list = [...plan.insure_list]
    if (list.includes(p)) {
      let index = list.findIndex(item => p === item)
      list.splice(index, 1)
      dispatchData('plan', { insure_list: [...list] })
    } else {
      list.push(p)
      list.sort()
      dispatchData('plan', { insure_list: [...list] })
    }
  }

  const handleExpDate = (dateType: string, dateDura: number, index?: number) => {
    switch (dateType) {
      case 'insured':
        if (!insured?.id_val_date) {
          Toast.info('请先选择被保人证件的开始有效期', 2, undefined, false)
          return
        }
        if (dateDura === 0) {
          dispatchData('insured', { id_exp_date: '9999-12-31' })
        } else {
          let _arr = insured.id_val_date.split('-')
          dispatchData('insured', {
            id_exp_date: `${parseInt(_arr[0]) + dateDura}-${_arr[1]}-${_arr[2]}`
          })
        }
        break
      case 'applicant':
        if (!applicant?.id_val_date) {
          Toast.info('请先选择投保人证件的开始有效期', 2, undefined, false)
          return
        }
        if (dateDura === 0) {
          dispatchData('applicant', { id_exp_date: '9999-12-31' })
        } else {
          let _arr = applicant.id_val_date.split('-')
          dispatchData('applicant', {
            id_exp_date: `${parseInt(_arr[0]) + dateDura}-${_arr[1]}-${_arr[2]}`
          })
        }
        break
      case 'benefit':
        let _beneficiary = JSON.parse(JSON.stringify(beneficiary))
        let _index = _beneficiary.findIndex(item => item.index === index)
        if (!_beneficiary[_index]?.id_val_date) {
          Toast.info('请先选择受益人证件的开始有效期', 2, undefined, false)
          return
        }

        if (dateDura === 0) {
          handleBeneficiary(index!, {
            id_exp_date: '9999-12-31'
          })
        } else {
          let _arr = _beneficiary[_index]?.id_val_date?.split('-')
          handleBeneficiary(index!, {
            id_exp_date: `${parseInt(_arr[0]) + dateDura}-${_arr[1]}-${_arr[2]}`
          })
        }
        break
    }
  }

  const handleInsureRate = useCallback(() => {
    let total = 0
    let info = {
      pay_dura: plan.insure_pay_dura,
      term_type: plan.insure_term_type,
      gender: plan.gender,
      age: getAgeByBirthDay(plan.brith_day),
      policy_amount: plan.insure_amount_n
    }

    console.log('info ->', info)
    plan.insure_list.forEach(insureItem => {
      let policy = policyData.find(item => {
        return (
          item.pay_dura === info.pay_dura &&
          item.term_type === info.term_type &&
          item.gender === info.gender &&
          item.age === info.age &&
          item.policy_type === insureItem
        )
      })

      let pay_amount = policy?.pay_amount || 0
      if (plan.insure_pay_type === 1) {
        total += Math.round(pay_amount * plan.insure_amount_n)
      } else if (plan.insure_pay_type === 4) {
        total += Math.round(pay_amount * plan.insure_amount_n * 0.09)
      }
    })

    dispatchData('plan', {
      insure_pay_amount: total || 0
    })
  }, [plan])

  const checkIsReadRule = () => {
    let isValid = true

    if (!(plan?.insure_pay_amount > 0)) {
      isValid = false
      Toast.info('请完善保障方案或调整保障方案中的各项参数', 2, undefined, false)
      planRef.current?.scrollIntoView()
      return isValid
    }

    if (!insureInfo.isReadRule) {
      isValid = false
      Toast.info('请勾选我已阅读', 2, undefined, false)
      introRef.current?.scrollIntoView()
    }
    return isValid
  }

  const checkInsured = () => {
    let isValid = true
    if (!isValidIdCard(insured.id_code)) {
      isValid = false
      Toast.info('被保险人身份证号码不正确', 2, undefined, false)
      insuredRef.current?.scrollIntoView()
      return isValid
    }

    if (getAge(insured.id_code) != getAgeByBirthDay(plan.brith_day)) {
      isValid = false
      Toast.info('被保险人身份证年龄与保险方案中保险人年龄不匹配', 2, undefined, false)
      insuredRef.current?.scrollIntoView()
      return isValid
    }

    if (getGender(insured.id_code) != plan.gender) {
      isValid = false
      Toast.info('被保险人身份证性别与保险方案中保险人性别不匹配', 2, undefined, false)
      insuredRef.current?.scrollIntoView()
      return isValid
    }

    if (!isValidName(insured.user_name)) {
      isValid = false
      Toast.info('被保险人姓名格式有误', 2, undefined, false)
      insuredRef.current?.scrollIntoView()
      return isValid
    }

    if (!isValidPhone(insured.mobile)) {
      isValid = false
      Toast.info('被保险人手机号码格式有误', 2, undefined, false)
      insuredRef.current?.scrollIntoView()
      return isValid
    }
    return isValid
  }

  const checkApplicant = () => {
    let isValid = true
    if (!isValidIdCard(applicant.id_code)) {
      isValid = false
      Toast.info('投保人身份证号码不正确', 2, undefined, false)
      applicantRef.current?.scrollIntoView()
      return isValid
    }

    if (!isValidName(insured.user_name)) {
      isValid = false
      Toast.info('投保人姓名格式有误', 2, undefined, false)
      applicantRef.current?.scrollIntoView()
      return isValid
    }

    if (!isValidPhone(insured.mobile)) {
      isValid = false
      Toast.info('投保人手机号码格式有误', 2, undefined, false)
      applicantRef.current?.scrollIntoView()
      return isValid
    }

    return isValid
  }

  const checkBenefit = () => {}

  // const checkBenefits = () => {
  //   const isValid = true;
  //   if (!beneficiary?.length) {
  //     return isValid;
  //   }

  // }

  const checkerPercent = () => {
    console.log('test')
    let result = {}
    let isValid = true
    if (beneficiary?.length) {
      beneficiary.forEach(item => {
        if (result[item.benefit_info.order]) {
          result[item.benefit_info.order].push(item.benefit_info.percent)
        } else {
          result[item.benefit_info.order] = [item.benefit_info.percent]
        }
      })

      console.log('result =>', result)
      let keys = Object.keys(result)
      for (let i = 0; i < keys.length; i++) {
        let total = 0
        result[keys[i]].forEach(item => {
          total += item
        })
        if (total !== 100) {
          isValid = false
          Toast.info(`受益顺序为${keys[i]}的受益人受益比例不等于100%`, 2, undefined, false)
          benefitRef.current?.scrollIntoView()
          break
        }
      }
    }
    return isValid
  }

  const checkAge = () => {
    if (insured.relation === 0) {
      if (!isValidSelfAge(insured.id_code)) {
        Toast.info('为本人投保，投保年龄应大于等于18周岁小于等于55周岁', 2, undefined, false)
        return false
      }
    } else {
      if (!checkApplicant()) {
        return false
      }

      if (!isValidApplicantAge(applicant.id_code)) {
        Toast.info('投保人年龄必须大于等于18周岁', 2, undefined, false)
        return false
      }
      if (!isValidInsured(insured.id_code)) {
        Toast.info('被保人年龄必须小于等于55周岁 大于等于28天', 2, undefined, false)
        return false
      }
    }
    return true
  }

  const submit = async () => {
    // 投保人年龄必须大于等于18周岁
    // 为本人投保，投保年龄应大于等于18周岁小于等于55周岁
    // 被保人年龄必须小于55周岁 大于28天
    if (checkerPercent() && checkIsReadRule() && checkInsured() && checkAge()) {
      const { data, error } = await request(`/insur_cii/api/policy/check`, 'POST', insure, true)
      if (error) {
        Toast.info(error, 2, undefined, false)
      } else {
        if (!data?.order_no) {
          Toast.info('核保接口没有返回订单号', 2, undefined, false)
        } else if (!data?.intelligent_url) {
          Toast.info('核保接口没有返回核保地址', 2, undefined, false)
        } else {
          dispatch({
            type: SET_INSURE_INFO,
            value: {
              order_no: data.order_no,
              intelligent_url: data.intelligent_url
            }
          })
          setIsShowRules(true)
        }
      }
    }
  }

  const getOss = async () => {
    const { data, error } = await request(`/insur_cii/api/picc/oss_key`, 'POST', insure, true)
    if (error) {
      console.log('erro2 =>', error)
      Toast.info(error, 2, undefined, false)
    } else {
      console.log('data2 =>', data)
      if (data) {
        setOss(data)
      }
    }
  }

  const upload = (imgType, imgName, ref) => {
    console.log('files0 =>', ref.current.files[0])
    const extName = ref.current.files[0]?.name.slice(ref.current.files[0]?.name?.lastIndexOf('.'))
    const fileName = oss.dir + user?.user_no + +new Date() + extName
    let formData = new FormData()
    formData.append('key', fileName)
    formData.append('OSSAccessKeyId', oss?.accessid)
    formData.append('policy', oss?.policy)
    formData.append('Signature', oss.signature)
    formData.append('success_action_status', '200') // 成功后返回的操作码
    formData.append('file', ref.current.files[0])
    fetch(oss.host, {
      method: 'POST',
      headers: {
        Accept: 'application/json'
      },
      body: formData
    })
      .then(res => {
        console.log('上传返回 =>', res)
        if (res?.status === 200) {
          Toast.info('上传成功')
          switch (imgType) {
            case 'insured':
              dispatchData('insured', {
                image_list: [
                  {
                    ...insured.image_list[0],
                    ['img_url_' + imgName]: `${oss.host}/${fileName}`
                  }
                ]
              })
              break

            case 'applicant':
              dispatchData('applicant', {
                image_list: [
                  {
                    ...applicant.image_list[0],
                    ['img_url_' + imgName]: `${oss.host}/${fileName}`
                  }
                ]
              })
              break
          }
        } else {
          throw new Error(res?.statusText)
        }
      })
      .catch(e => {
        console.log('异常报错 =>', e?.toString())
        Toast.info(e?.message || '影像上传失败')
      })
      .finally(() => {
        ref.current.value = null
      })
  }

  const navigate = path => {
    dispatch({
      type: SET_INSURE_INFO,
      value: {
        isFromRule: true
      }
    })
    history.push(path)
  }

  useEffect(() => {
    getShareMaterial({
      url: 'index',
      title: '利好消息！退役军人家庭《终身重疾惠军版》来了！',
      text: '确诊疾病可一次性获得一笔保障金，最高可达75万元！立即获取保障！',
      imgUrl: 'https://cii.huijun365.com//slogan.jpg'
    })

    if (localStorage.getItem('token')) {
      getOss()
    }

    if (insureInfo.isFromRule) {
      introRef.current?.scrollIntoView()
    }
  }, [])

  useEffect(() => {
    handleInsureRate()
  }, [
    plan.insure_pay_dura,
    plan.brith_day,
    plan.gender,
    plan.insure_term_type,
    plan.insure_amount_n,
    plan.insure_pay_type,
    plan.insure_list
  ])

  useEffect(() => {
    let _payDuras: Set<number> = new Set()
    let _termTypes: Set<number> = new Set()
    policyData
      .filter(item => item.age === getAgeByBirthDay(plan.brith_day) && item.gender === plan.gender)
      ?.forEach(item => {
        _payDuras.add(item.pay_dura)
        _termTypes.add(item.term_type as any)
      })
    setPayDuras([..._payDuras])
    setTermTypes([..._termTypes])
  }, [plan.brith_day, plan.gender])

  useEffect(() => {
    let _total = 0
    let _amount = plan.insure_amount_n || 0

    plan.insure_list?.forEach(index => {
      _total += insuranceRate[index] * _amount
    })
    console.log('_total =>', _total)
    setTotalInsurance(_total)
  }, [plan.insure_list, plan.insure_amount_n])

  return (
    <div className="index-page">
      <div className="body">
        <div className="table_card">
          <Header />
          <div className="card" ref={planRef}>
            <h2 className="title">保障方案选择</h2>
            <DatePicker
              value={plan?.brith_day ? new Date(plan.brith_day) : new Date()}
              mode="date"
              minDate={new Date(new Date().getFullYear() - 56, 1, 1)}
              maxDate={new Date()}
              onOk={v => {
                dispatchData('plan', { brith_day: dayjs(v).format('YYYY-MM-DD') })
              }}>
              <div className="programme_row">
                <span>被保险人生日</span>
                <span>{plan?.brith_day}</span>
                <img src={arrow} className="arrow" alt="" />
              </div>
            </DatePicker>
            <div className="programme_row">
              <span>被保险人性别</span>
              <div className="programme_right">
                <span
                  className={`btn ${plan?.gender === 'M' ? 'active' : ''}`}
                  onClick={() => {
                    dispatchData('plan', { gender: 'M' })
                  }}>
                  男
                </span>
                <span
                  className={`btn ${plan?.gender === 'F' ? 'active' : ''}`}
                  onClick={() => {
                    dispatchData('plan', { gender: 'F' })
                  }}>
                  女
                </span>
              </div>
            </div>
            {termTypes?.length ? (
              <div className="programme_row">
                <span>保险期间</span>
                <div className="programme_right">
                  {termTypes?.map(term => {
                    return (
                      <span
                        className={`btn ${plan?.insure_term_type === term ? 'active' : ''}`}
                        onClick={() => {
                          dispatchData('plan', { insure_term_type: term })
                        }}>
                        {term === 1 ? '至保险人70周岁' : '终身'}
                      </span>
                    )
                  })}
                  {/* <span
                  className={`btn ${plan?.insure_term_type === 2 ? 'active' : ''}`}
                  onClick={() => {
                    dispatchData('plan', { insure_term_type: 2 })
                  }}>
                  终身
                </span>
                <span
                  className={`btn ${plan?.insure_term_type === 1 ? 'active' : ''}`}
                  onClick={() => {
                    dispatchData('plan', { insure_term_type: 1 })
                  }}>
                  至保险人70周岁
                </span> */}
                </div>
              </div>
            ) : null}
            <div className="programme_row">
              <span>保险金额</span>
            </div>
            <div className="programme_row" style={{ justifyContent: 'flex-start' }}>
              <span
                className={`btn ${plan?.insure_amount_n === 10 ? 'active' : ''}`}
                onClick={() => {
                  dispatchData('plan', { insure_amount_n: 10 })
                }}>
                10万
              </span>
              <span
                className={`btn ${plan?.insure_amount_n === 20 ? 'active' : ''}`}
                onClick={() => {
                  dispatchData('plan', { insure_amount_n: 20 })
                }}>
                20万
              </span>
              <span
                className={`btn ${plan?.insure_amount_n === 30 ? 'active' : ''}`}
                onClick={() => {
                  dispatchData('plan', { insure_amount_n: 30 })
                }}>
                30万
              </span>
              <span
                className={`btn ${[10, 20, 30].includes(plan?.insure_amount_n) ? '' : 'active'}`}
                style={{ position: 'relative' }}
                onClick={() => {
                  setIsShowCustomAmount(true)
                }}>
                {[10, 20, 30].includes(plan?.insure_amount_n)
                  ? '自定义金额'
                  : plan?.insure_amount_n + '万'}
              </span>
            </div>
            {payDuras?.length ? (
              <div className="programme_row">
                <span>交费期间</span>
                <div className="programme_right">
                  {payDuras?.map(dura => {
                    return (
                      <span
                        className={`btn ${plan?.insure_pay_dura === dura ? 'active' : ''}`}
                        onClick={() => {
                          dispatchData('plan', { insure_pay_dura: dura })
                        }}>
                        {dura}年交
                      </span>
                    )
                  })}
                  {/* <span
                  className={`btn ${plan?.insure_pay_dura === 10 ? 'active' : ''}`}
                  onClick={() => {
                    dispatchData('plan', { insure_pay_dura: 10 })
                  }}>
                  10年交
                </span>
                <span
                  className={`btn ${plan?.insure_pay_dura === 20 ? 'active' : ''}`}
                  onClick={() => {
                    dispatchData('plan', { insure_pay_dura: 20 })
                  }}>
                  20年交
                </span>
                <span
                  className={`btn ${plan?.insure_pay_dura === 30 ? 'active' : ''}`}
                  onClick={() => {
                    dispatchData('plan', { insure_pay_dura: 30 })
                  }}>
                  30年交
                </span> */}
                </div>
              </div>
            ) : null}
            <div className="programme_row">
              <span>交费方式</span>
              <div className="programme_right">
                <span
                  className={`btn ${plan?.insure_pay_type === 1 ? 'active' : ''}`}
                  onClick={() => {
                    dispatchData('plan', { insure_pay_type: 1 })
                  }}>
                  年交
                </span>
                <span
                  className={`btn ${plan?.insure_pay_type === 4 ? 'active' : ''}`}
                  onClick={() => {
                    dispatchData('plan', { insure_pay_type: 4 })
                  }}>
                  月交
                </span>
              </div>
            </div>
            <div className="programme_row" style={{ paddingBottom: 0, border: 'none' }}>
              <span>可选保障责任(可多选)</span>
            </div>
            <div
              className={`programme_responsibility ${
                plan?.insure_list?.includes(2) ? 'active' : ''
              }`}
              onClick={() => handleClickPlan(2)}>
              <label>未投保</label>
              <h2>
                可选保障责任一{' '}
                <img
                  src={question}
                  alt=""
                  onClick={e => {
                    e.stopPropagation()
                    setIsShowResponsibility(1)
                  }}
                />
              </h2>
              <h4>
                <span>疾病关爱保险金</span>
                <img src={plan?.insure_list?.includes(2) ? checked : unchecked} alt="" />
              </h4>
            </div>
            <div
              className={`programme_responsibility ${
                plan?.insure_list?.includes(3) ? 'active' : ''
              }`}
              onClick={() => handleClickPlan(3)}>
              <label>未投保</label>
              <h2>
                可选保障责任二
                <img
                  src={question}
                  alt=""
                  onClick={e => {
                    e.stopPropagation()
                    setIsShowResponsibility(2)
                  }}
                />
              </h2>
              <h4>
                <span>重大疾病扩展保险金</span>
                <img src={plan?.insure_list?.includes(3) ? checked : unchecked} alt="" />
              </h4>
            </div>
            <div
              className={`programme_responsibility ${
                plan?.insure_list?.includes(4) ? 'active' : ''
              }`}
              onClick={() => handleClickPlan(4)}>
              <label>未投保</label>
              <h2>
                可选保障责任三{' '}
                <img
                  src={question}
                  alt=""
                  onClick={e => {
                    e.stopPropagation()
                    setIsShowResponsibility(3)
                  }}
                />
              </h2>
              <h4>
                <span>重度恶性肿瘤扩展保险金</span>
                <img src={plan?.insure_list?.includes(4) ? checked : unchecked} alt="" />
              </h4>
            </div>
            <div
              className={`programme_responsibility ${
                plan?.insure_list?.includes(5) ? 'active' : ''
              }`}
              onClick={() => handleClickPlan(5)}>
              <label>未投保</label>
              <h2>
                可选保障责任四
                <img
                  src={question}
                  alt=""
                  onClick={e => {
                    e.stopPropagation()
                    setIsShowResponsibility(4)
                  }}
                />
              </h2>
              <h4>
                <span>特定心脑血管疾病扩展保险金</span>
                <img src={plan?.insure_list?.includes(5) ? checked : unchecked} alt="" />
              </h4>
            </div>
          </div>

          {plan?.insure_pay_amount > 0 ? (
            <div className="card" ref={insuredRef}>
              <h2 className="title">填写投保信息</h2>
              <div className="insured_row" style={{ border: 'none', paddingBottom: 0 }}>
                <div className="insured_prefix">为谁投保</div>
              </div>
              <div className="insured_row">
                <div className="insured_center" style={{ alignItems: 'center', display: 'flex' }}>
                  <div
                    className={`btn ${insured?.relation === 0 ? 'active' : ''}`}
                    onClick={() => {
                      dispatchData('insured', { id_type: 1, relation: 0 })
                    }}>
                    本人
                  </div>
                  {/* <div
                    className={`btn ${insured?.relation === 1 ? 'active' : ''}`}
                    onClick={() => {
                      dispatchData('insured', { id_type: 1, relation: 1 })
                    }}>
                    配偶
                  </div> */}
                  <div
                    className={`btn ${insured?.relation === 3 ? 'active' : ''}`}
                    onClick={() => {
                      dispatchData('insured', { relation: 3 })
                    }}>
                    子女
                  </div>
                  {/* <div
                    className={`btn ${insured?.relation === 2 ? 'active' : ''}`}
                    onClick={() => {
                      dispatchData('insured', { id_type: 1, relation: 2 })
                    }}>
                    父母
                  </div> */}
                </div>
              </div>
              <div className="insured_row">
                <div className="insured_prefix">姓名</div>
                <div className="insured_center">
                  <input
                    className="input"
                    placeholder="请输入被保险人姓名"
                    value={insured?.user_name}
                    onChange={e => {
                      dispatchData('insured', {
                        user_name: e.target.value?.trim()
                      })
                    }}
                  />
                </div>
              </div>
              <div className="insured_row">
                <div className="insured_prefix">证件类型</div>
                <div className="insured_center">
                  <span
                    className={`btn ${insured?.id_type === 1 ? 'active' : ''}`}
                    onClick={() => {
                      dispatchData('insured', {
                        id_type: 1,
                        image_list: [{ ...(insured?.image_list?.[0] || {}), image_type: 1 }]
                      })
                    }}>
                    身份证
                  </span>
                  {insured?.relation === 2 ? (
                    <span
                      className={`btn ${insured?.id_type === 7 ? 'active' : ''}`}
                      onClick={() => {
                        dispatchData('insured', {
                          id_type: 7,
                          image_list: [{ ...(insured?.image_list?.[0] || {}), image_type: 7 }]
                        })
                      }}>
                      户口簿
                    </span>
                  ) : null}
                </div>
              </div>
              <div className="insured_row">
                <div className="insured_prefix">证件号码</div>
                <div className="insured_center">
                  <input
                    className="input"
                    placeholder="请输入被保险人的证件号码"
                    value={insured?.id_code}
                    onChange={e => {
                      let value = e.target.value?.trim()
                      if (isValidIdCard(value)) {
                        dispatchData('insured', { id_code: value })
                        dispatchData('plan', {
                          brith_day: `${value.substring(6, 10)}-${value.substring(
                            10,
                            12
                          )}-${value.substring(12, 14)}`,
                          gender: getGender(value)
                        })
                      } else {
                        dispatchData('insured', { id_code: value })
                      }
                    }}
                  />
                </div>
              </div>
              {insured?.id_type === 1 ? (
                <>
                  <div className="insured_row" style={{ border: 'none', paddingBottom: 0 }}>
                    <div className="insured_center" style={{ fontWeight: 500 }}>
                      证件有效期
                    </div>
                  </div>

                  <DatePicker
                    mode="date"
                    minDate={new Date(1900, 1, 1)}
                    maxDate={new Date(2100, 12, 30)}
                    onOk={v => {
                      dispatchData('insured', { id_val_date: dayjs(v).format('YYYY-MM-DD') })
                    }}>
                    <div className="insured_row">
                      <div className="insured_prefix">开始日期</div>
                      <div className="insured_center">
                        <span>{insured?.id_val_date || '请选择开始有效日期'}</span>
                      </div>
                      <img src={arrow} className="arrow" />
                    </div>
                  </DatePicker>

                  <div className="insured_row">
                    <div className="insured_prefix">有效期限</div>
                    <div className="insured_center">
                      <span
                        className={`btn ${
                          parseInt(insured?.id_exp_date) ===
                          (insured?.id_val_date && parseInt(insured?.id_val_date) + 5)
                            ? 'active'
                            : ''
                        }`}
                        onClick={() => {
                          handleExpDate('insured', 5)
                        }}>
                        5年
                      </span>
                      <span
                        className={`btn ${
                          parseInt(insured?.id_exp_date) ===
                          (insured?.id_val_date && parseInt(insured?.id_val_date) + 10)
                            ? 'active'
                            : ''
                        }`}
                        onClick={() => {
                          handleExpDate('insured', 10)
                        }}>
                        10年
                      </span>
                      <span
                        className={`btn ${
                          parseInt(insured?.id_exp_date) ===
                          (insured?.id_val_date && parseInt(insured?.id_val_date) + 20)
                            ? 'active'
                            : ''
                        }`}
                        onClick={() => {
                          handleExpDate('insured', 20)
                        }}>
                        20年
                      </span>
                      <span
                        className={`btn ${insured?.id_exp_date === '9999-12-31' ? 'active' : ''}`}
                        onClick={() => {
                          handleExpDate('insured', 0)
                        }}>
                        长期
                      </span>
                    </div>
                  </div>
                </>
              ) : null}

              {totalInsurance >= 50 ? (
                <>
                  <div className="insured_row" style={{ paddingBottom: 0 }}>
                    <div className="insured_center" style={{ fontWeight: 500 }}>
                      上传影像
                    </div>
                  </div>
                  <div className="image_row">
                    <div className="image_list">
                      <div className="image_item">
                        <div className="image">
                          {insured?.image_list?.[0]?.img_url_1 ? (
                            <img src={insured?.image_list?.[0]?.img_url_1} className="photo" />
                          ) : (
                            <img src={ic_upload} className="upload" />
                          )}
                          <input
                            className="input_upload"
                            style={{ opacity: 0 }}
                            type="file"
                            accept="image/*"
                            id="insured_img1"
                            ref={insuredImg1Ref}
                            onChange={e => {
                              upload('insured', 1, insuredImg1Ref)
                            }}
                          />
                        </div>
                        <div className="image_desc">
                          {insured?.id_type === 1 ? '正面影像' : '被保人页'}
                        </div>
                      </div>
                      <div className="image_item">
                        <div className="image">
                          {insured?.image_list?.[0]?.img_url_2 ? (
                            <img src={insured?.image_list?.[0]?.img_url_2} className="photo" />
                          ) : (
                            <img src={ic_upload} className="upload" />
                          )}
                          <input
                            className="input_upload"
                            style={{ opacity: 0 }}
                            type="file"
                            accept="image/*"
                            ref={insuredImg2Ref}
                            onChange={e => {
                              upload('insured', 2, insuredImg2Ref)
                            }}
                          />
                        </div>
                        <div className="image_desc">
                          {insured?.id_type === 1 ? '反面影像' : '投保人页'}
                        </div>
                      </div>
                      {insured?.id_type === 7 ? (
                        <div className="image_item">
                          <div className="image">
                            {insured?.image_list?.[0]?.img_url_3 ? (
                              <img src={insured?.image_list?.[0]?.img_url_3} className="photo" />
                            ) : (
                              <img src={ic_upload} className="upload" />
                            )}
                            <input
                              className="input_upload"
                              style={{ opacity: 0 }}
                              type="file"
                              accept="image/*"
                              ref={insuredImg3Ref}
                              onChange={e => {
                                upload('insured', 3, insuredImg3Ref)
                              }}
                            />
                          </div>
                          <div className="image_desc">户主页</div>
                        </div>
                      ) : null}
                    </div>
                  </div>
                </>
              ) : null}

              <div className="insured_row">
                <div className="insured_prefix">手机号码</div>
                <div className="insured_center">
                  <input
                    className="input"
                    placeholder="请输入被保险人的手机号码"
                    value={insured.mobile}
                    onChange={e => {
                      dispatchData('insured', {
                        mobile: e.target.value?.trim()
                      })
                    }}
                  />
                </div>
              </div>
              <Picker
                title="选择通信地址类型"
                extra="请选择(可选)"
                data={addressTypes}
                cols={1}
                style={{ width: '100%' }}
                value={insured?.contact_info?.address_type}
                onChange={() => {}}
                onOk={v => {
                  console.log('ok =>', v)
                  dispatchData('insured', {
                    contact_info: { ...insured.contact_info, address_type: v[0] }
                  })
                }}>
                <div className="insured_row">
                  <div className="insured_prefix">地址类型</div>

                  <div className="insured_center">
                    <span>
                      {
                        addressTypes?.find(
                          (item: any) => item.value === insured?.contact_info?.address_type
                        )?.label
                      }
                    </span>
                  </div>
                  <img src={arrow} className="arrow" alt="" />
                </div>
              </Picker>

              <Picker
                title="选择地区"
                extra="请选择(可选)"
                data={antdDistrict}
                style={{ width: '100%' }}
                // value={city}
                onOk={(v: any) => {
                  console.log('ok =>', v)
                  dispatchData('insured', {
                    contact_info: {
                      ...insured.contact_info,
                      region_code: antdDistrictLabels[v.join('-')] || '',
                      region: v.join('-')
                    }
                  })
                }}>
                <div className="insured_row">
                  <div className="insured_prefix">所在地区</div>
                  <div className="insured_center">
                    <span>{insured?.contact_info?.region}</span>
                  </div>
                  <img src={arrow} className="arrow" alt="" />
                </div>
              </Picker>

              <div className="insured_row">
                <div className="insured_prefix">联系地址</div>
                <div className="insured_center">
                  <input
                    className="input"
                    placeholder="被保人的联系地址(不少于12个汉字)"
                    value={insured?.contact_info?.address}
                    onChange={e => {
                      dispatchData('insured', {
                        contact_info: { ...insured.contact_info, address: e.target.value }
                      })
                    }}
                  />
                </div>
              </div>

              <Picker
                title="请选择职业"
                extra="请选择"
                data={occupationData as any}
                style={{ width: '100%' }}
                // value={applicant?.occupation_type}
                onChange={(v: any) => {
                  console.log('change =>', v)
                  dispatchData('insured', { occupation_type: v[2] })
                }}
                onOk={(v: any) => {
                  dispatchData('insured', { occupation_type: v[2] })
                }}>
                <div className="insured_row">
                  <div className="insured_prefix">职业</div>
                  <div className="insured_center">
                    <span>
                      {antdOccupation.find(item => item.value === insured?.occupation_type)
                        ?.label || '请选择职业'}
                    </span>
                  </div>
                  <img src={arrow} className="arrow" />
                </div>
              </Picker>
            </div>
          ) : null}

          {plan?.insure_pay_amount > 0 && applicant ? (
            <div className="card" ref={applicantRef}>
              <h2 className="title">投保人信息</h2>
              <div className="applicant_row">
                <div className="applicant_prefix">姓名</div>
                <div className="applicant_center">
                  <input
                    className="input"
                    placeholder="请输入保险人姓名"
                    value={applicant?.user_name}
                    onChange={e => {
                      dispatchData('applicant', { user_name: e.target.value?.trim() })
                    }}
                  />
                </div>
              </div>
              <div className="applicant_row">
                <div className="applicant_prefix">证件号码</div>
                <div className="applicant_center">
                  <input
                    className="input"
                    placeholder="请输入保险人的证件号码"
                    value={applicant?.id_code}
                    onChange={e => {
                      dispatchData('applicant', { id_code: e.target.value?.trim() })
                    }}
                  />
                </div>
              </div>
              <div className="applicant_row" style={{ border: 'none', paddingBottom: 0 }}>
                <div className="applicant_center" style={{ fontWeight: 500 }}>
                  证件有效期
                </div>
              </div>
              <DatePicker
                mode="date"
                minDate={new Date(1900, 1, 1)}
                maxDate={new Date(2100, 12, 30)}
                onOk={v => {
                  dispatchData('applicant', { id_val_date: dayjs(v).format('YYYY-MM-DD') })
                }}>
                <div className="applicant_row">
                  <div className="applicant_prefix">开始日期</div>
                  <div className="applicant_center">
                    <span>{applicant?.id_val_date || '请选择开始有效日期'}</span>
                  </div>
                  <img src={arrow} className="arrow" />
                </div>
              </DatePicker>

              <div className="applicant_row">
                <div className="applicant_prefix">有效期限</div>
                <div className="applicant_center">
                  <span
                    className={`btn ${
                      parseInt(applicant?.id_exp_date) ===
                      (applicant?.id_val_date && parseInt(applicant?.id_val_date) + 5)
                        ? 'active'
                        : ''
                    }`}
                    onClick={() => {
                      handleExpDate('applicant', 5)
                    }}>
                    5年
                  </span>
                  <span
                    className={`btn ${
                      parseInt(applicant?.id_exp_date) ===
                      (applicant?.id_val_date && parseInt(applicant?.id_val_date) + 10)
                        ? 'active'
                        : ''
                    }`}
                    onClick={() => {
                      handleExpDate('applicant', 10)
                    }}>
                    10年
                  </span>
                  <span
                    className={`btn ${
                      parseInt(applicant?.id_exp_date) ===
                      (applicant?.id_val_date && parseInt(applicant?.id_val_date) + 20)
                        ? 'active'
                        : ''
                    }`}
                    onClick={() => {
                      handleExpDate('applicant', 20)
                    }}>
                    20年
                  </span>
                  <span
                    className={`btn ${applicant?.id_exp_date === '9999-12-31' ? 'active' : ''}`}
                    onClick={() => {
                      handleExpDate('applicant', 0)
                    }}>
                    长期
                  </span>
                </div>
              </div>

              {/* <div className="applicant_date">
                <DatePicker
                  mode="date"
                  minDate={new Date(1900, 1, 1)}
                  maxDate={new Date(2100, 12, 30)}
                  onOk={v => {
                    dispatchData('applicant', { id_val_date: dayjs(v).format('YYYY-MM-DD') })
                  }}>
                  <span>{applicant?.id_val_date || '开始日期'}</span>
                </DatePicker>
                <span className="split">至</span>
                <DatePicker
                  mode="date"
                  minDate={new Date(1900, 1, 1)}
                  maxDate={new Date(2100, 12, 30)}
                  onOk={v => {
                    dispatchData('applicant', { id_exp_date: dayjs(v).format('YYYY-MM-DD') })
                  }}>
                  <span>{applicant?.id_exp_date || '结束日期'}</span>
                </DatePicker>
                <span
                  className="id_long_date"
                  onClick={() => {
                    dispatchData('applicant', { id_exp_date: '9999-12-31' })
                  }}>
                  长期有效
                </span>
              </div> */}

              {totalInsurance >= 50 && insured?.relation !== 0 ? (
                <>
                  <div className="insured_row" style={{ paddingBottom: 0 }}>
                    <div className="insured_center" style={{ fontWeight: 500 }}>
                      上传影像
                    </div>
                  </div>
                  <div className="image_row">
                    <div className="image_list">
                      <div className="image_item">
                        <div className="image">
                          {applicant?.image_list?.[0]?.img_url_1 ? (
                            <img src={applicant?.image_list?.[0]?.img_url_1} className="photo" />
                          ) : (
                            <img src={ic_upload} className="upload" />
                          )}
                          <input
                            className="input_upload"
                            style={{ opacity: 0 }}
                            type="file"
                            accept="image/*"
                            id="insured_img1"
                            ref={applicantImg1Ref}
                            onChange={e => {
                              upload('applicant', 1, applicantImg1Ref)
                            }}
                          />
                        </div>
                        <div className="image_desc">
                          {insured?.id_type === 1 ? '正面影像' : '被保人页'}
                        </div>
                      </div>
                      <div className="image_item">
                        <div className="image">
                          {applicant?.image_list?.[0]?.img_url_2 ? (
                            <img src={applicant?.image_list?.[0]?.img_url_2} className="photo" />
                          ) : (
                            <img src={ic_upload} className="upload" />
                          )}
                          <input
                            className="input_upload"
                            style={{ opacity: 0 }}
                            type="file"
                            accept="image/*"
                            ref={applicantImg2Ref}
                            onChange={e => {
                              upload('applicant', 2, applicantImg2Ref)
                            }}
                          />
                        </div>
                        <div className="image_desc">
                          {insured?.id_type === 1 ? '反面影像' : '投保人页'}
                        </div>
                      </div>
                      {insured?.id_type === 7 ? (
                        <div className="image_item">
                          <div className="image">
                            {applicant?.image_list?.[0]?.img_url_3 ? (
                              <img src={applicant?.image_list?.[0]?.img_url_3} className="photo" />
                            ) : (
                              <img src={ic_upload} className="upload" />
                            )}
                            <input
                              className="input_upload"
                              style={{ opacity: 0 }}
                              type="file"
                              accept="image/*"
                              ref={applicantImg3Ref}
                              onChange={e => {
                                upload('applicant', 3, applicantImg2Ref)
                              }}
                            />
                          </div>
                          <div className="image_desc">户主页</div>
                        </div>
                      ) : null}
                    </div>
                  </div>
                </>
              ) : null}

              <div className="applicant_row">
                <div className="applicant_prefix">手机号码</div>
                <div className="applicant_center">
                  <input
                    className="input"
                    placeholder="请输入保险人的手机号码"
                    value={applicant?.mobile}
                    onChange={e => {
                      dispatchData('applicant', { mobile: e.target.value?.trim() })
                    }}
                  />
                </div>
              </div>

              <Picker
                title="选择通信地址类型"
                extra="请选择(可选)"
                data={addressTypes}
                cols={1}
                style={{ width: '100%' }}
                value={applicant?.contact_info?.address_type}
                onChange={() => {}}
                onOk={v => {
                  console.log('ok =>', v)
                  dispatchData('applicant', {
                    contact_info: { ...applicant.contact_info, address_type: v[0] }
                  })
                }}>
                <div className="applicant_row">
                  <div className="applicant_prefix">地址类型</div>

                  <div className="applicant_center">
                    <span>
                      {
                        addressTypes?.find(
                          (item: any) => item.value === applicant?.contact_info?.address_type
                        )?.label
                      }
                    </span>
                  </div>
                  <img src={arrow} className="arrow" alt="" />
                </div>
              </Picker>

              <Picker
                title="选择地区"
                extra="请选择(可选)"
                data={antdDistrict}
                style={{ width: '100%' }}
                // value={city}
                onOk={(v: any) => {
                  console.log('ok =>', v[2])
                  dispatchData('applicant', {
                    contact_info: {
                      ...applicant.contact_info,
                      region_code: antdDistrictLabels[v.join('-')] || '',
                      region: v.join('-')
                    }
                  })
                }}>
                <div className="applicant_row">
                  <div className="applicant_prefix">所在地区</div>
                  <div className="applicant_center">
                    <span>{applicant?.contact_info?.region}</span>
                  </div>
                  <img src={arrow} className="arrow" alt="" />
                </div>
              </Picker>

              <div className="applicant_row">
                <div className="applicant_prefix">联系地址</div>
                <div className="applicant_center">
                  <input
                    className="input"
                    placeholder="投保人的联系地址(不少于12个汉字)"
                    value={applicant?.contact_info?.address}
                    onChange={e => {
                      dispatchData('applicant', {
                        contact_info: { ...applicant.contact_info, address: e.target.value }
                      })
                    }}
                  />
                </div>
              </div>

              <Picker
                title="请选择职业"
                extra="请选择"
                data={occupationData as any}
                style={{ width: '100%' }}
                // value={applicant?.occupation_type}
                onChange={(v: any) => {
                  console.log('change =>', v)
                  dispatchData('applicant', { occupation_type: v[2] })
                }}
                onOk={(v: any) => {
                  dispatchData('applicant', { occupation_type: v[2] })
                }}>
                <div className="applicant_row">
                  <div className="applicant_prefix">职业</div>
                  <div className="applicant_center">
                    <span>
                      {antdOccupation.find(item => item.value === applicant?.occupation_type)
                        ?.label || '请选择职业'}
                    </span>
                  </div>
                  <img src={arrow} className="arrow" />
                </div>
              </Picker>
            </div>
          ) : null}

          {plan?.insure_pay_amount > 0 ? (
            <div className="card" ref={benefitRef}>
              <h2 className="title">缴费银行帐户信息</h2>
              <Picker
                title="选择银行"
                extra="请选择"
                data={bankData}
                cols={1}
                style={{ width: '100%' }}
                value={bank?.bank_number}
                onChange={v => {
                  console.log('change =>', v)
                  dispatchData('bank', { bank_number: v?.[0] })
                }}
                onOk={(v: any) => {
                  console.log('ok =>', v)
                  dispatchData('bank', { bank_number: v?.[0] })
                }}>
                <div className="applicant_row">
                  <div className="applicant_prefix">缴费银行</div>
                  <div className="applicant_center">
                    <span>
                      {bankData?.find((item: any) => item.value === bank?.bank_number)?.label}
                    </span>
                  </div>
                  <img src={arrow} className="arrow" alt="" />
                </div>
              </Picker>
              <div className="applicant_row">
                <div className="applicant_prefix">银行帐户名</div>
                <div className="applicant_center">
                  <input
                    className="input"
                    placeholder="请输入银行帐户姓名"
                    value={bank.account_name}
                    onChange={e => {
                      dispatchData('bank', {
                        account_name: e.target.value?.trim()
                      })
                    }}
                  />
                </div>
              </div>
              <div className="applicant_row">
                <div className="applicant_prefix">银行账号</div>
                <div className="applicant_center">
                  <input
                    className="input"
                    placeholder="请输入缴费银行帐户账号"
                    value={bank.account_number}
                    onChange={e => {
                      dispatchData('bank', {
                        account_number: e.target.value?.trim()
                      })
                    }}
                  />
                </div>
              </div>
            </div>
          ) : null}

          {plan?.insure_pay_amount > 0 ? (
            <div className="card">
              <h2 className="title">受益人信息</h2>
              <div className="beneficiary">
                <div
                  className={`btn ${insureInfo?.isLegalBenefit ? 'active' : ''}`}
                  onClick={() => {
                    dispatch({
                      type: SET_INSURE_INFO,
                      value: {
                        isLegalBenefit: true
                      }
                    })
                    dispatchData('beneficiary', {
                      beneficiary: []
                    })
                  }}>
                  法定受益人
                </div>
                <div
                  className={`btn ${insureInfo?.isLegalBenefit ? '' : 'active'}`}
                  onClick={() => {
                    dispatch({
                      type: SET_INSURE_INFO,
                      value: {
                        isLegalBenefit: false
                      }
                    })
                  }}>
                  指定受益人
                </div>
              </div>
            </div>
          ) : null}

          {plan?.insure_pay_amount > 0
            ? beneficiary?.map((benefit, index) => {
                return (
                  <div className="card" key={benefit.index}>
                    <div className="benefit">
                      <h4>
                        受益人{index + 1}{' '}
                        <img
                          src={minus}
                          onClick={() => {
                            let _beneficiary = JSON.parse(JSON.stringify(beneficiary))
                            let _index = _beneficiary.findIndex(
                              item => item.index === benefit.index
                            )
                            _beneficiary.splice(_index, 1)
                            dispatchData('beneficiary', {
                              beneficiary: _beneficiary
                            })
                          }}
                        />
                      </h4>
                      <div className="benefit_row" style={{ border: 'none', paddingBottom: 0 }}>
                        <div className="benefit_row_prefix">与被保人关系</div>
                      </div>
                      <div className="benefit_row">
                        <div className="benefit_row_center" style={{ height: 'auto' }}>
                          <div
                            className={`btn ${benefit?.relation === 0 ? 'active' : ''}`}
                            onClick={() => {
                              handleBeneficiary(benefit.index, { id_type: 1, relation: 0 })
                            }}>
                            本人
                          </div>
                          <div
                            className={`btn ${benefit?.relation === 1 ? 'active' : ''}`}
                            onClick={() => {
                              handleBeneficiary(benefit.index, { id_type: 1, relation: 1 })
                            }}>
                            配偶
                          </div>
                          <div
                            className={`btn ${benefit?.relation === 2 ? 'active' : ''}`}
                            onClick={() => {
                              handleBeneficiary(benefit.index, { relation: 2 })
                            }}>
                            子女
                          </div>
                          <div
                            className={`btn ${benefit?.relation === 3 ? 'active' : ''}`}
                            onClick={() => {
                              handleBeneficiary(benefit.index, { id_type: 1, relation: 3 })
                            }}>
                            父母
                          </div>
                        </div>
                      </div>
                      <div className="benefit_row">
                        <div className="benefit_row_prefix">姓名</div>
                        <div className="benefit_row_center">
                          <input
                            type="text"
                            placeholder="请输入受益人姓名"
                            value={benefit.user_name}
                            onChange={e => {
                              handleBeneficiary(benefit.index, {
                                user_name: e.target.value?.trim()
                              })
                            }}
                          />
                        </div>
                      </div>
                      <div className="benefit_row">
                        <div className="benefit_row_prefix">证件类型</div>
                        <div className="benefit_row_center" style={{ height: 'auto' }}>
                          <span
                            className={`btn ${benefit?.id_type === 1 ? 'active' : ''}`}
                            onClick={() => {
                              handleBeneficiary(benefit.index, { id_type: 1 })
                            }}>
                            身份证
                          </span>
                          {benefit.relation === 2 ? (
                            <span
                              className={`btn ${benefit?.id_type === 7 ? 'active' : ''}`}
                              onClick={() => {
                                handleBeneficiary(benefit.index, {
                                  id_type: 7,
                                  id_val_date: '',
                                  id_exp_date: ''
                                })
                              }}>
                              户口簿
                            </span>
                          ) : null}
                        </div>
                      </div>

                      <div className="benefit_row">
                        <div className="benefit_row_prefix">证件号码</div>
                        <div className="benefit_row_center">
                          <input
                            className="input"
                            placeholder="请输入受益人的证件号码"
                            value={benefit?.id_code}
                            onChange={e => {
                              handleBeneficiary(benefit.index, {
                                id_code: e.target.value?.trim()
                              })
                            }}
                          />
                        </div>
                      </div>

                      {benefit?.id_type === 1 ? (
                        <>
                          <div className="benefit_row" style={{ border: 'none', paddingBottom: 0 }}>
                            <div className="benefit_row_prefix">证件有效期</div>
                          </div>

                          <DatePicker
                            mode="date"
                            minDate={new Date(1900, 1, 1)}
                            maxDate={new Date(2100, 12, 30)}
                            onOk={v => {
                              handleBeneficiary(benefit.index, {
                                id_val_date: dayjs(v).format('YYYY-MM-DD')
                              })
                            }}>
                            <div className="benefit_row">
                              <div className="benefit_prefix">开始日期</div>
                              <div className="benefit_center">
                                <span>{benefit?.id_val_date || '请选择开始有效日期'}</span>
                              </div>
                              <img src={arrow} className="arrow" />
                            </div>
                          </DatePicker>

                          <div className="benefit_row">
                            <div className="benefit_prefix">有效期限</div>
                            <div className="benefit_center">
                              <span
                                className={`btn ${
                                  parseInt(benefit?.id_exp_date) ===
                                  (benefit?.id_val_date && parseInt(benefit?.id_val_date) + 5)
                                    ? 'active'
                                    : ''
                                }`}
                                onClick={() => {
                                  handleExpDate('benefit', 5, benefit.index)
                                }}>
                                5年
                              </span>
                              <span
                                className={`btn ${
                                  parseInt(benefit?.id_exp_date) ===
                                  (benefit?.id_val_date && parseInt(benefit?.id_val_date) + 10)
                                    ? 'active'
                                    : ''
                                }`}
                                onClick={() => {
                                  handleExpDate('benefit', 10, benefit.index)
                                }}>
                                10年
                              </span>
                              <span
                                className={`btn ${
                                  parseInt(benefit?.id_exp_date) ===
                                  (benefit?.id_val_date && parseInt(benefit?.id_val_date) + 20)
                                    ? 'active'
                                    : ''
                                }`}
                                onClick={() => {
                                  handleExpDate('benefit', 20, benefit.index)
                                }}>
                                20年
                              </span>
                              <span
                                className={`btn ${
                                  benefit?.id_exp_date === '9999-12-31' ? 'active' : ''
                                }`}
                                onClick={() => {
                                  handleExpDate('benefit', 0, benefit.index)
                                }}>
                                长期
                              </span>
                            </div>
                          </div>

                          {/* <div className="benefit_date">
                            <DatePicker
                              mode="date"
                              minDate={new Date(1900, 1, 1)}
                              maxDate={new Date(2100, 12, 30)}
                              onOk={v => {
                                handleBeneficiary(benefit.index, {
                                  id_val_date: dayjs(v).format('YYYY-MM-DD')
                                })
                              }}>
                              <span>{benefit?.id_val_date || '开始日期'}</span>
                            </DatePicker>
                            <span className="split">至</span>
                            <DatePicker
                              mode="date"
                              minDate={new Date(1900, 1, 1)}
                              maxDate={new Date(2100, 12, 30)}
                              onOk={v => {
                                handleBeneficiary(benefit.index, {
                                  id_exp_date: dayjs(v).format('YYYY-MM-DD')
                                })
                              }}>
                              <span>{benefit?.id_exp_date || '结束日期'}</span>
                            </DatePicker>
                            <span
                              className="id_long_date"
                              onClick={() => {
                                handleBeneficiary(benefit.index, { id_exp_date: '9999-12-31' })
                              }}>
                              长期有效
                            </span>
                          </div> */}
                        </>
                      ) : null}
                      <div className="benefit_row">
                        <div className="benefit_row_prefix">手机号码</div>
                        <div className="benefit_row_center">
                          <input
                            type="text"
                            placeholder="请输入受益人手机号码"
                            value={benefit.mobile}
                            onChange={e => {
                              handleBeneficiary(benefit.index, { mobile: e.target.value?.trim() })
                            }}
                          />
                        </div>
                      </div>
                      <Picker
                        title="选择通信地址类型"
                        extra="请选择"
                        data={addressTypes}
                        cols={1}
                        style={{ width: '100%' }}
                        value={benefit?.contact_info?.address_type}
                        onChange={() => {}}
                        onOk={v => {
                          console.log('ok =>', v)
                          handleBeneficiary(benefit.index, {
                            contact_info: { ...benefit.contact_info, address_type: v[0] }
                          })
                        }}>
                        <div className="benefit_row">
                          <div className="benefit_row_prefix">地址类型</div>

                          <div className="benefit_row_center">
                            <span>
                              {
                                addressTypes?.find(
                                  (item: any) => item.value === benefit?.contact_info?.address_type
                                )?.label
                              }
                            </span>
                          </div>
                          <img src={arrow} className="arrow" alt="" />
                        </div>
                      </Picker>

                      <Picker
                        title="选择地区"
                        extra="请选择(可选)"
                        data={antdDistrict}
                        style={{ width: '100%' }}
                        // value={city}
                        onOk={(v: any) => {
                          console.log('ok =>', v[2])
                          handleBeneficiary(benefit.index, {
                            contact_info: {
                              ...benefit.contact_info,
                              region_code: antdDistrictLabels[v.join('-')] || '',
                              region: v.join('-')
                            }
                          })
                        }}>
                        <div className="benefit_row">
                          <div className="benefit_row_prefix">所在地区</div>
                          <div className="benefit_row_center">
                            <span>{benefit?.contact_info?.region}</span>
                          </div>
                          <img src={arrow} className="arrow" alt="" />
                        </div>
                      </Picker>

                      <div className="benefit_row">
                        <div className="benefit_row_prefix">联系地址</div>
                        <div className="benefit_row_center">
                          <input
                            className="input"
                            placeholder="受益人的联系地址(不少于12个汉字)"
                            value={benefit?.contact_info?.address}
                            onChange={e => {
                              handleBeneficiary(benefit.index, {
                                contact_info: { ...benefit.contact_info, address: e.target.value }
                              })
                            }}
                          />
                        </div>
                      </div>

                      <Picker
                        title="请选择职业"
                        extra="请选择"
                        data={occupationData as any}
                        style={{ width: '100%' }}
                        // value={applicant?.occupation_type}
                        onOk={(v: any) => {
                          console.log('occupation =>', v)
                          handleBeneficiary(benefit.index, {
                            occupation_type: v[2]
                          })
                        }}>
                        <div className="benefit_row">
                          <div className="benefit_row_prefix">职业</div>
                          <div className="benefit_row_center">
                            <span>
                              {antdOccupation.find(item => item.value === benefit?.occupation_type)
                                ?.label || '请选择职业'}
                            </span>
                          </div>
                          <img src={arrow} className="arrow" />
                        </div>
                      </Picker>

                      <Picker
                        title="选择受益顺序"
                        extra="请选择"
                        data={benefitOrders}
                        cols={1}
                        style={{ width: '100%' }}
                        value={benefit?.contact_info?.address_type}
                        onChange={() => {}}
                        onOk={v => {
                          console.log('ok =>', v)
                          handleBeneficiary(benefit.index, {
                            benefit_info: { ...benefit.benefit_info, order: v[0] }
                          })
                        }}>
                        <div className="benefit_row">
                          <div className="benefit_row_prefix">受益顺序</div>
                          <div className="benefit_row_center">
                            <span>
                              {benefitOrders?.find(
                                (item: any) => item.value === benefit?.benefit_info?.order
                              )?.label || '请选择受益顺序'}
                            </span>
                          </div>
                          <img src={arrow} className="arrow" />
                        </div>
                      </Picker>

                      <div className="benefit_row">
                        <div className="benefit_row_prefix">受益比例</div>
                        <div className="benefit_row_center">
                          <input
                            className="input"
                            placeholder="请输入1-100的数字"
                            value={benefit?.benefit_info?.percent}
                            onChange={e => {
                              let _percent = parseInt(e.target.value.trim()) || 0
                              if (_percent < 0) {
                                _percent = 0
                              } else if (_percent > 100) {
                                _percent = 100
                              }
                              handleBeneficiary(benefit.index, {
                                benefit_info: { ...benefit.benefit_info, percent: _percent }
                              })
                            }}
                          />
                        </div>
                        <div className="benefit_row_suffix">%</div>
                      </div>
                    </div>
                  </div>
                )
              })
            : null}

          {insureInfo?.isLegalBenefit ? null : (
            <div
              className="benefit_add"
              onClick={() => {
                let _benifit = { ...BENEFIT }
                let _length = insure?.user_list?.length
                _benifit.index = insure?.user_list[_length - 1].index + 1
                dispatchData('beneficiary', {
                  beneficiary: [...beneficiary, _benifit]
                })
              }}>
              <img src={plus} alt="" />
              <span>添加受益人</span>
            </div>
          )}

          <img src={index_insurance} className="index_insurance" alt="" />
          <img
            src={index_upgrade}
            className="index_upgrade"
            alt=""
            onClick={() => {
              history.push('/viewer?path=valueaddedservice')
            }}
          />

          <div className="card">
            <h2 className="title">
              <span>常见问题</span>
            </h2>
            <>
              {questions?.map((item, index) => {
                return (
                  <div className="question">
                    <h4
                      onClick={() => {
                        let _list = [...questions]
                        _list[index].show = !_list[index].show
                        setQuestions(_list)
                      }}>
                      <span>
                        <img src={ic_question} />
                        {item.q}
                      </span>
                      <span>
                        <img src={arrow} className="arrow" />
                      </span>
                    </h4>
                    {item.show ? <p>{item.a}</p> : null}
                  </div>
                )
              })}
            </>
          </div>
          <div className="card">
            <p className="desc">
              1.保险公司不得违规销售非保险金融产品，请勿参与非法集资;2.我司默认为您制作电子保单，电子保单与纸质合同具有同等效力，如需纸质保单请拨打客服热线95518转寿险。
            </p>
            <img src={index_logo} className="index_logo" />
          </div>

          <div className="introductions" ref={introRef}>
            <div
              className={`radio ${insureInfo.isReadRule ? 'active' : ''}`}
              // onClick={() => {
              //   dispatch({
              //     type: SET_INSURE_INFO,
              //     value: {
              //       isReadRule: !insureInfo.isReadRule
              //     }
              //   })
              // }}
            ></div>
            <div className="introduction">
              请您阅读
              <span
                onClick={() => {
                  navigate('/viewer?path=specification')
                }}>
                《产品说明书》
              </span>
              <span
                onClick={() => {
                  navigate('/viewer?path=notice')
                }}>
                《投保须知》
              </span>
              <span
                onClick={() => {
                  navigate('/viewer?path=privacy')
                }}>
                《隐私政策》
              </span>
              <span
                onClick={() => {
                  navigate('/viewer?path=guide')
                }}>
                《理赔服务指引》
              </span>
              <span
                onClick={() => {
                  navigate('/viewer?path=prompt')
                }}>
                《人身保险投保提示》
              </span>
              <span
                onClick={() => {
                  navigate('/viewer?path=authorize')
                }}>
                《投保人、被保险人声明和授权》
              </span>
              及
              <span
                onClick={() => {
                  navigate('/viewer?path=clauses')
                }}>
                《保险条款》
              </span>
              全部内容，知晓本合同将于支付成功后次日生效，同意将生效日当天设定为犹豫期的初始日期。
            </div>
          </div>
        </div>
      </div>
      <footer>
        <div className="service">
          <img
            src={icon_service}
            onClick={() => {
              history.push('/customerService')
            }}
          />
          <span>客服</span>
        </div>
        <div className="desc">
          <span>首期保费：</span>
          <strong>
            {plan.insure_pay_amount}元/{plan?.insure_pay_type === 1 ? '年' : '月'}
          </strong>
        </div>
        <div
          className="index_btn"
          id='立即投保'
          onClick={() => {
            submit()
            // setIsShowEstimate(true);
          }}>
          立即投保
        </div>
      </footer>

      {/* {isShowEstimate ? (
        <EstimateModal
          isShowEstimate={isShowEstimate}
          setIsShowEstimate={setIsShowEstimate}
          setIsShowRules={setIsShowRules}
        />
      ) : null} */}

      {isShowCustomAmount ? (
        <CustomAmount
          dispatchData={dispatchData}
          amount={plan.insure_amount_n * 10000}
          setIsShowCustomAmount={setIsShowCustomAmount}
        />
      ) : null}

      {isShowResponsibility ? (
        <Responsibility
          setIsShowResponsibility={setIsShowResponsibility}
          responsibility={isShowResponsibility}
        />
      ) : null}

      {isShowRules ? <Rules setIsShowRules={setIsShowRules} /> : null}

      <BlueCardEndModal />
    </div>
  )
}

export default Index
