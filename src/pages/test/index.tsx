/*
 * @Author: w<PERSON><PERSON><PERSON>
 * @Date: 2024-06-24 17:47:26
 * @LastEditors: wuqiang
 * @LastEditTime: 2024-07-14 23:17:47
 */
import React, { useEffect, useState } from 'react'
import { useHistory } from 'react-router-dom'
import './index.scss'

const About = () => {
  const history = useHistory()
  const [token, setToken] = useState<string | null>('')

  useEffect(() => {
    setToken(localStorage.getItem('token') || '')
    // localStorage.setItem('token', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVaWQiOjEsImV4cCI6MTcyMDkyNDczNSwiaWF0IjoxNzIwMzE5OTM1LCJpc3MiOiJZYW5QZW5nSksiLCJuYmYiOjE3MjAzMTk5MzV9.foB3lv9CxCDxoFtcN3WcFVuBGQCwLmRW2mxGEKiWCz4');
    // setToken('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVaWQiOjEsImV4cCI6MTcyMDkyNDczNSwiaWF0IjoxNzIwMzE5OTM1LCJpc3MiOiJZYW5QZW5nSksiLCJuYmYiOjE3MjAzMTk5MzV9.foB3lv9CxCDxoFtcN3WcFVuBGQCwLmRW2mxGEKiWCz4')
  }, [])

  return (
    <div className="test-page">
      <span>{token}</span>
    </div>
  )
}

export default About
