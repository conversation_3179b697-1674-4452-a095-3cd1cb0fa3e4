.caculator_modal_mask {
  position: fixed;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.5);
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  .caculator_modalN {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 314.15px;
    height: 487.65px;
    box-sizing: border-box;
    padding: 0 40px;
    background: url('../../../../assets/caculator_result_modalN.png') 50% 50% / cover no-repeat;
    h2 {
      font-weight: bold;
      font-size: 18px;
      color: #832604;
      margin-top: 25px;
      margin-bottom: 20px;
    }
    .caculator_result_row {
      display: flex;
      align-items: center;
      width: 100%;
      font-size: 14px;
      color: #7A2815;
      font-weight: 500;
      line-height: 24px;
      height: 24px;
      strong {
        color: red;
        font-size: 18px;
        font-weight: 500;
        margin: 0 10px;
      }
      .calculator_result_input {
        width: 56px;
        height: 25px;
        line-height: 25px;
        color: #7A2815;
        background-color: #faeed3;
        border: 1px solid #ffa72a;
        margin: 0 5px;
      }
    }
    .caculator_result_desc {
      position: relative;
      width: 100%;
      padding-top: 20px;
      margin: 15px auto;
      padding-bottom: 10px;
      font-weight: 500;
      color: #FA3800;
      font-size: 12px;
      line-height: 18px;
      strong {
        font-size: 15px;
      }
      .arrow_left {
        position: absolute;
        left: 0; 
        top: 0;
        width: 36px;
        height: auto;
      }
      .arrow_right {
        position: absolute;
        right: 0; 
        bottom: 0;
        width: 36px;
        height: auto;
      }
    }
    .caculator_result_btn {
      width: 236px;
    }
    .caculator_qrcode {
      width: 68px;
      height: 68px;
      margin-top: 5px;
    }
    .caculator_qrcode_desc {
      width: 100%;
      font-size: 10px;
      text-align: center;
      line-height: 15px;
      margin-top: 5px;
    }
  }
}
