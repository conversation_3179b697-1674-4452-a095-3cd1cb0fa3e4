/*
 * @Author: w<PERSON><PERSON><PERSON>
 * @Date: 2024-07-25 16:17:37
 * @LastEditors: wuqiang
 * @LastEditTime: 2024-09-24 11:02:48
 */
import React, { useCallback, useEffect, useState } from 'react'
import { useHistory } from 'react-router-dom'

import caculator_arrow from '@/assets/caculator_arrow.png'
import caculator_btn from '@/assets/caculator_btn.png'
import ic_caculator_custom from '@/assets/ic_caculator_custom.png'
import ic_caculator_percent from '@/assets/ic_caculator_percent.png'
import './index.scss'
import { useDispatch, useSelector } from 'react-redux'
import { SET_INSURE_DETAIL } from '@/constants'
import { CaculatorAmount } from './components/CaculatorAmount'
import { CaculatorModal } from './components/CaculatorModal'
import { APPLICANT, BENEFIT } from '@/store/reducers/insure'
import { DatePicker, Toast } from 'antd-mobile'
import dayjs from 'dayjs'
import { getAgeByBirthDay, isValidApplicantAge, isValidName, isValidPhone } from '@/utils'
import policyData from '@/constants/policy_rates.json'
import request from '@/api/request'
import BlueCardEndModal from '@/components/BlueCardEndModal'

const Caculator = () => {
  const history = useHistory()
  const dispatch = useDispatch()
  const [isShowCustomAmount, setIsShowCustomAmout] = useState(false)
  const [isShowCaculatorResult, setIsShowCaculatorResult] = useState(false)
  const insured = useSelector((state: any) =>
    state.insure?.user_list?.find((item: any) => item.user_type === 1)
  ) // 被保人
  const plan = useSelector((state: any) => state.insure?.insure_plan)
  const insure = useSelector((state: any) => state.insure)

  const record = async () => {
    const { data, error } = await request(`/insur_cii/api/user/calculator`, 'POST', {
      name: insured.user_name,
      mobile: insured.mobile,
      age: getAgeByBirthDay(plan.brith_day),
    }, true)
    if (error) {
      Toast.info(error, 2, undefined, false)
    } else {
      setIsShowCaculatorResult(true)
    }
  }

  const dispatchData = (type: string, obj: Record<string, any>) => {
    let _user_list = JSON.parse(JSON.stringify(insure.user_list))
    switch (type) {
      case 'plan':
        dispatch({
          type: SET_INSURE_DETAIL,
          value: {
            insure_plan: {
              ...plan,
              ...obj
            }
          }
        })
        break
      case 'insured':
        let _insured = JSON.parse(JSON.stringify(insured))
        _insured = { ..._insured, ...obj }
        console.log('_insured =>', _insured)
        let _index = insure.user_list.findIndex(item => item.user_type === 1)
        let _applicant_index_1 = insure.user_list.findIndex(item => item.user_type === 2)
        _user_list[_index] = _insured

        switch (obj.relation) {
          case 0:
            if (_applicant_index_1 >= 0) {
              _user_list.splice(_applicant_index_1, 1)
            }
            break
          case 1:
          case 2:
          case 3:
            if (_applicant_index_1 < 0) {
              _user_list.splice(1, 0, APPLICANT)
            }
        }
        console.log('_user_list =>', _user_list)
        dispatch({
          type: SET_INSURE_DETAIL,
          value: {
            user_list: _user_list
          }
        })
        break
    }
  }

  const handleInsureRate = useCallback(() => {
    let total = 0
    let info = {
      pay_dura: plan.insure_pay_dura,
      term_type: plan.insure_term_type,
      gender: plan.gender,
      age: getAgeByBirthDay(plan.brith_day),
      policy_amount: plan.insure_amount_n
    }

    console.log('info ->', info)
    plan.insure_list.forEach(insureItem => {
      let policy = policyData.find(item => {
        return (
          item.pay_dura === info.pay_dura &&
          item.term_type === info.term_type &&
          item.gender === info.gender &&
          item.age === info.age &&
          item.policy_type === insureItem
        )
      })

      let pay_amount = policy?.pay_amount || 0
      if (plan.insure_pay_type === 1) {
        total += Math.round(pay_amount * plan.insure_amount_n)
      } else if (plan.insure_pay_type === 4) {
        total += Math.round(pay_amount * plan.insure_amount_n * 0.09)
      }
    })

    dispatchData('plan', {
      insure_pay_amount: total || 0
    })
  }, [plan])


  useEffect(() => {
    handleInsureRate()
  }, [
    plan.insure_pay_dura,
    plan.brith_day,
    plan.gender,
    plan.insure_term_type,
    plan.insure_amount_n,
    plan.insure_pay_type,
    plan.insure_list
  ])

  return (
    <div className="caculator-page">
      <div className="caculator_form">
        <div className="caculator_paper">
          我们人生中渴望美好，往往会忽略疾病风险的防
          <br />
          范，当疾病发生在我们自己身上，自己的
          <br />
          治疗费，家庭的各项开支如何去保持。我们需
          <br />
          要获得一笔补偿金来补充。
        </div>
        <div className="caculator_desc">
          <h4 className="subtitle">在您意外或生病的情况下，无法正常工作</h4>
          <h2 className="title">您希望一次性领取多少补偿金</h2>
          <img src={caculator_arrow} className="arrow_left" alt="" />
          <img src={caculator_arrow} className="arrow_right" alt="" />
        </div>
        <div className="caculator_table">
          <div className="caculator_row">
            <div className="caculator_label">姓 名：</div>
            <div className="caculator_input">
              <input
                className="input"
                value={insured?.user_name}
                onChange={e => {
                  dispatchData('insured', {
                    user_name: e.target.value?.trim()
                  })
                }}
              />
            </div>
          </div>
          <DatePicker
              value={plan?.brith_day ? new Date(plan.brith_day) : new Date()}
              mode="date"
              minDate={new Date(new Date().getFullYear() - 56, 1, 1)}
              maxDate={new Date()}
              onOk={v => {
                dispatchData('plan', { brith_day: dayjs(v).format('YYYY-MM-DD') })
              }}>
              <div className="caculator_row">
            <div className="caculator_label">出生日期：</div>
            <div className="caculator_input">
              <input type="nunber" readOnly value={plan?.brith_day} />
            </div>
          </div>
            </DatePicker>
          
          <div className="caculator_row">
            <div className="caculator_label">手机号码：</div>
            <div className="caculator_input">
              <input
                type="text"
                value={insured.mobile}
                onChange={e => {
                  dispatchData('insured', {
                    mobile: e.target.value?.trim()
                  })
                }}
              />
            </div>
          </div>
          {/* <div className="caculator_row">
            <div className="caculator_label">验 证 码：</div>
            <div className="caculator_input">
              <input type="text" placeholder="请输入验证码" value={insured.code}
                    onChange={e => {
                      dispatchData('insured', {
                        code: e.target.value?.trim()
                      })
                    }} />
              <span className="code_placeholder">发送验证码</span>
            </div>
          </div> */}
          <div className="caculator_row">
            <div className="caculator_label">性 别：</div>
            <div className="caculator_input_checkbox">
              <label>
                <span>男</span>
                <input
                  type="radio"
                  checked={plan?.gender === 'M' ? true : false}
                  onClick={e => {
                    dispatchData('plan', { gender: 'M' })
                  }}
                />
              </label>
              <label>
                <span>女</span>
                <input
                  type="radio"
                  checked={plan?.gender === 'F' ? true : false}
                  onChange={e => {
                    dispatchData('plan', { gender: 'F' })
                  }}
                />
              </label>
            </div>
          </div>
          <div className="caculator_row">
            <div className="caculator_label">选择补偿金：</div>
            <div className="caculator_compensate">
              <div className="caculator_compensate_amount">
                {plan?.insure_amount_n}万
                {plan?.insure_amount_n === 10 ? (
                  <img src={ic_caculator_percent} className="percent" alt="" />
                ) : null}
              </div>
            </div>
            <img
              src={ic_caculator_custom}
              className="caculator_compensate_custom"
              alt=""
              onClick={() => {
                setIsShowCustomAmout(true)
              }}
            />
          </div>
        </div>
        <img src={caculator_btn} className="caculator_btn" alt="" onClick={() => {
          if (!plan.brith_day || (getAgeByBirthDay(plan.brith_day) > 55)) {
            Toast.info('请确保填写了生日，并且年龄不得大于55周岁');
            return;
          }
          if (!isValidName(insured?.user_name)) {
            Toast.info('请填写有效的姓名格式', 2, undefined, false);
            return;
          }

          if (!isValidPhone(insured?.mobile)) {
            Toast.info('手机号码格式有误', 2, undefined, false);
            return;
          }

          // if (!insured?.code) {
          //   Toast.info('请填写验证码', 2, undefined, false);
          //   return;
          // }
          // setIsShowCaculatorResult(true)
          record()
        }} />
      </div>

      {isShowCustomAmount ? (
        <CaculatorAmount
          setIsShowCustomAmout={setIsShowCustomAmout}
          dispatchData={dispatchData}
          plan={plan}
        />
      ) : null}
      {isShowCaculatorResult ? (
        <CaculatorModal setIsShowCaculatorResult={setIsShowCaculatorResult} plan={plan} insured={insured} />
      ) : null}

<BlueCardEndModal />
    </div>
  )
}

export default Caculator
