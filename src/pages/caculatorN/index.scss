.caculator-page {
  width: 100%;
  min-height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #d10700 url('../../assets/caculator_bg.png') 50% 0 / contain no-repeat;
  .caculator_form {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 245px;
    box-sizing: border-box;
    width: 375px;
    height: 887.5px;
    background: url('../../assets/caculator_frame.png') 50% 0% / cover no-repeat;
  }
  .caculator_paper {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 296.5px;
    height: 150.5px;
    color: #fff;
    font-size: 12px;
    line-height: 23px;
    background: url('../../assets/caculator_paper.png') 50% 0% / cover no-repeat;
  }
  .caculator_desc {
    position: relative;
    width: 296.5px;
    margin-top: 20px;
    padding-top: 20px;
    padding-bottom: 20px;
    .title {
      font-size: 14px;
      color: #FA3800;
      text-align: center;
      font-weight: bold;
    }
    .subtitle {
      font-size: 12px;
      font-weight: 400;
      color: #FA3800;
      line-height: 21.5px;
      text-align: center;
    }
    .arrow_left {
      position: absolute;
      left: 10px;
      top: 0;
      width: 40px;
      height: 15px;
    }
    .arrow_right {
      position: absolute;
      right: 10px;
      bottom: 0;
      width: 40px;
      height: 15px;
    }
  }
  .caculator_table {
    width: 296.5px;
    margin-top: 25px;
    .caculator_row {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      .caculator_label {
        color: rgba(122, 40, 21, 1);
        font-size: 13px;
        flex: 1;
        text-align: justify;
      }
      .caculator_input_checkbox {
        width: 230px;
        height: 35px;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        font-size: 13px;
        label {
          margin-right: 10px;
          display: flex;
          align-items: center;
          span {
            margin-right: 2px;
          }
        }
      }
      .caculator_input {
        position: relative;
        width: 230px;
        height: 35px;
        box-sizing: border-box;
        border: 1px solid #FFA72A;
        border-radius: 4px;
        input {
          width: 230px;
          height: 35px;
          line-height: 35px;
          border: 0 none;
          padding: 0 5px;
          font-size: 13px;
        }
        .code_placeholder {
          position: absolute;
          right: 0;
          top: 0;
          font-size: 13px;
          color: red;
          width: 80px;
          text-align: center;
          line-height: 33px;
          height: 35px;
        }
      }
      .caculator_compensate {
        display: flex;
        align-items: center;
        width: 140px;
        text-align: left;
        font-size: 13px;
        color:rgba(122, 40, 21, 1)
      }
      .caculator_compensate_amount {
        position: relative;
        
      }
      .caculator_compensate_custom {
        width: 72.5px;
        height: 20px;
      }
    }
  }
  .caculator_btn {
    width: 254.4px;
    height: 35px;
    margin-top: 15px;
  }
  img.percent {
    position: absolute;
    top: -18px;
    right: -50px;
    width: 50px;
    height: auto;
  }
}