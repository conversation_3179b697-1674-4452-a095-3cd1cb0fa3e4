import React, { Fragment, useEffect, useState } from 'react'
import avatar from '@/assets/avatar.png'
import eye from '@/assets/minenew/eye.png'
import eye2 from '@/assets/minenew/eye2.png'
import iicon from '@/assets/minenew/i.png'
import jiangbei from '@/assets/minenew/jiangbei.png'
import clickReport from '@/utils/clickReport'
import { Button, Toast } from 'antd-mobile'
import { useDispatch, useSelector } from 'react-redux'
import { RESET_USER_INFO, SET_USER_INFO, IS_CLICK_LOGIN, SEARCH_DATA } from '@/constants'
import { getShareMaterial } from '@/utils/share'
import { isWechat } from '@/utils'
import qs from 'qs'
import request from '@/api/request'
import './index.scss'
import SearchModal from './SearchModal'
import Api, { SearchResponseData, SearchResponseDataLocal } from './api'
import DetailModal from './DetailModal'

const local: any = window.location
const search = local.search.slice(1)
const code: string = (qs.parse(search) as any)['code']
const appid = 'wx47eb7cb22dd360e3'

const defaultData = {
  "policy_code": "***************",
  "policy_status": "1",
  "apply_date": "2025-07-21",
  "effect_date": "2025-07-22",
  "end_date": "9999-09-09",
  "first_premium": "",
  "first_charge_type": "5",
  "pay_type": "",
  "addup_status": "1",
  "addup_type": "4",
  "addup_amount": "150",
  "is_tax_policy": "N",
  "total_payment_amt": "150.00",
  "policy_account_value": "147.75",
  "applicant_name": "赵杨",
  "applicant_mobile": "***********",
  "applicant_id_type": "1",
  "applicant_idno": "110111198702185510",
  "insurant_name": "赵杨",
  "insurant_mobile": "",
  "insurant_id_type": "1",
  "insurant_idno": "110111198702185510"
}

const MineNewPage = () => {
  const [detailData, setDetailData] = useState<SearchResponseData | null>(null)
  const [show, setShow] = useState(false)
  const [showDetail, setShowDetail] = useState(false)
  const [showAmount, setShowAmount] = useState(true)
  const [searchData, setSearchData] = useState<SearchResponseDataLocal[]>([])
  const dispatch = useDispatch();
  const userInfo = useSelector((state: any) => state?.user)
  const toggleShowAmount = () => {
    setShowAmount(!showAmount)
  }

  const getUser = async () => {
    const { data, error } = await request(`/insur_cii/api/user/info`, 'POST', {}, true);
    if (!error) {
      dispatch({ type: SET_USER_INFO, value: { ...data, token: localStorage.getItem('token') } })
    } else {
      Toast.info(error || '当前同时在线人数过多，请您稍后查看!', 2)
      if (error) {
        // token 过期提示

      }
    }
  }

  const wxAuthLogin = async () => {
    const { data, error } = await request(`/insur_cii/api/wechat/auth_login`, 'GET', { code }, true);
    if (!error) {
      localStorage.setItem('token', data?.token || '')
      dispatch({ type: SET_USER_INFO, value: data || {} });
      await getUser();
      await fetchOrderList();
    } else {
      console.log('error =>', error);
      Toast.info(error || '当前同时在线人数过多，请您稍后查看!', 2)
    }
    localStorage.removeItem(IS_CLICK_LOGIN)
  }

  const gotoWxPageAuth = async () => {
    // 非静默授权，第一次有弹框
    // if (process.env.NODE_ENV === 'development') return
    window.location.href =
      'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' +
      appid +
      '&redirect_uri=' +
      encodeURIComponent(local.href) +
      '&response_type=code&scope=snsapi_userinfo&state=1#wechat_redirect'
  }

  const clickView = async () => {
    await clickReport(`退伍军人重疾保险个人中心-mineNew-点击税优按钮`);
    // Toast.info('申办通道未开启，敬请等待通知')
    window.location.href = 'https://hmb.yuanxinhuibao.com/veterans_mobile_2025/#/guid?type=1'
  }

  const clickView2 = async () => {
    await clickReport(`退伍军人重疾保险个人中心-mineNew-点击非税优版按钮`);
    // Toast.info('申办通道未开启，敬请等待通知')
    window.location.href = `https://hmb.yuanxinhuibao.com/veterans_mobile_2025/#/guid?type=2`
  }

  const closeModal = () => {
    setShow(false)
  }

  const openModal = () => {
    setShow(true)
  }

  const openDetail = (detailData: SearchResponseData) => {
    setDetailData(detailData)
    setShowDetail(true)
  }

  const closeDetail = () => {
    setShowDetail(false)
  }

  const onSearch = (value: SearchResponseDataLocal[]) => {
    if (searchData.length > 0) {
      setSearchData((state) => {
        const newState = state.concat(value)
        return newState;
      })
    } else {
      setSearchData(value)
    }
    if (value.length > 0) {
      setShow(false)
    } else {
      Toast.show('没有查询到您的相关信息')
    }
  }

  // 退出登录清空 用户信息和token
  const handleLogout = () => {
    dispatch({ type: RESET_USER_INFO })
    localStorage.clear()
    // 退出以后重新刷新页面
    window.location.href = window.location.origin + window.location.pathname
  }

  const handleLogin = () => {
    if (isWechat()) {
      // 如果点击了登录，做一个标志，方便重定向回调做判断
      localStorage.setItem(IS_CLICK_LOGIN, '1');
      gotoWxPageAuth()
    } else {
      Toast.info('请使用微信浏览器打开!')
    }
  }

  const handleSearch = () => {
    openModal()
    // Toast.info('开发中，敬请期待')
  }

  const fetchOrderList = async () => {
    try {
      const response = await Api.getOrderListLocal()
      // localStorage.setItem(SEARCH_DATA, JSON.stringify(search))
      if (response.data.length > 0) {
        setSearchData(response.data)
      }
    } catch (error) {
      Toast.info('查询失败')
    }
  }

  const refresh = async (item: SearchResponseDataLocal) => {
    if (item.applicant_idno && item.applicant_name && item.policy_list[0].applicant_mobile) {
      try {
        const response = await Api.getOrderList({ username: item.applicant_name, id_no: item.applicant_idno, mobile: item.policy_list[0].applicant_mobile })
        const handleData = response.data;
        const newSearchData = searchData.map((newItem: SearchResponseDataLocal) => {
          if (newItem.applicant_idno === item.applicant_idno) {
            newItem.policy_list = handleData
          }
          return newItem
        })
        setSearchData(newSearchData)
      } catch (error) {
        Toast.info('刷新数据失败！')
      }
    } else {
      Toast.info('刷新数据失败！')
    }
  }

  useEffect(() => {
    getShareMaterial({
      url: 'bgPage',
      title: '利好消息！退役军人家庭《终身重疾惠军版》来了！',
      text: '确诊疾病可一次性获得一笔保障金，最高可达75万元！立即获取保障！',
      imgUrl: 'https://cii.huijun365.com//head-img.png'
    })
  }, [])

  // 判断有token，就获取用户信息。
  useEffect(() => {
    console.log('first userInfo', userInfo)
    // 如果有token，直接获取用户信息
    if (userInfo?.token || localStorage.getItem('token')) {
      getUser();
    } else {
      // 如果没有token，判断是否网页是否有code参数
      if (!code) {
        // 没有code参数
      } else {
        // 有code参数就是微信重定向后的回调，判断是否是点击登录后的重定向。
        const isClickLogin = localStorage.getItem(IS_CLICK_LOGIN)
        // 如果是点击登录后的重定向，就调用登录接口
        if (isClickLogin) {
          wxAuthLogin();
        }
      }
    }
    // if (isWechat()) {
    //   getCode()
    // } else {
    //   Toast.info('请使用微信浏览器打开!')
    // }
    // new VConsole();
  }, [])

  useEffect(() => {
    // const condition = localStorage.getItem(SEARCH_DATA)
    // if (condition) {
    //   const search = JSON.parse(condition)
    //   fetchOrderList(search)
    // }
    fetchOrderList();
  }, [])

  return (
    <div className='mine-new-page'>
      <div className='mine-new-page__header'>
        <div className='mine-new-page__header__card'>
          <div className='mine-new-page__header__card__avatar'>
            <div className='avatar'>
              <img src={userInfo.head_img || avatar} />
            </div>
            <div className='username'>
              {userInfo.nickname || '游客'}
            </div>
          </div>
          {/* <div className='mine-new-page__header__card__info'></div> */}
          <div className='mine-new-page__header__card__btnbox'>
            <div className='logout-btn' onClick={userInfo.token ? handleLogout : handleLogin}>
              <span className='icon'><img width={10} height={10} src={jiangbei} /></span>
              <span>{userInfo.token ? '退出登录' : '点击登录'}</span>
            </div>
          </div>
        </div>
      </div>
      <div className='mine-new-page__account'>
        <div className='mine-new-page__account__bar'>
          <div>我的账户</div>
          {/* {
            userInfo.token ?
              <div className='my-policy'>
                <div className='my-policy-bg'>
                  我的保单
                </div>
              </div>
              : null
          } */}
        </div>
        {
          // searchData.length === 0 && userInfo.token ?
          <div className='search-btn-area'>
            <div className='search-btn-area__bg'>
              <button onClick={handleSearch}>点击登录账户</button>
            </div>
          </div>
          // : null
        }
        <div className='mine-new-page__account__area'>

          {searchData.length === 0 ?
            (<div className='mine-new-page__account__cardnone'>暂无数据</div>)
            :
            searchData.map(item => {
              return (
                <Fragment key={item.applicant_idno}>
                  <Button onClick={() => refresh(item)}>刷新-{item.applicant_name}</Button>
                  <div className='mine-new-page__account__card' key={item.applicant_idno}>
                    {/* <div className='mine-new-page__account__card__userinfo'>
                    <div>{item.applicant_name}</div>
                    <div>{item.applicant_idno}</div>
                  </div> */}
                    {
                      item.policy_list.map(item2 => {
                        return (
                          <Fragment key={item2.policy_code}>
                            <div className='mine-new-page__account__card__title'>
                              <div className='title-left'>
                                <div>已交保费(元)</div>
                                <div className='icon' onClick={toggleShowAmount}>
                                  <img width={14} height={10} src={showAmount ? eye : eye2} alt='eye' />
                                </div>
                              </div>
                              <div className='title-right'>
                                <div onClick={() => { openDetail(item2) }}>查看详情</div>
                              </div>
                            </div>
                            <div className='mine-new-page__account__card__amount'>
                              {showAmount ? '****' : item2.total_payment_amt}
                            </div>
                          </Fragment>
                        )
                      })
                    }
                    {/* <div className='mine-new-page__account__card__content'>
                    <div className='content-left'>
                      本年度可转入额度
                      <div className='icon'>
                        <img width={12} height={12} src={iicon} alt='eye' />
                      </div>
                    </div>
                  </div> */}
                  </div>
                </Fragment>
              )
            })

          }

        </div>
      </div>
      <div className='mine-new-page__service'>
        <div className='mine-new-page__service__text'>
          <div className='title-text__bold'>退役军人家庭专属养老金</div>
          <div className='title-text'>创新设立双收益账户，享行业领先收益水平</div>
        </div>
        <div className='mine-new-page__service__earningcard'>
          <div className='mine-new-page__service__earningcard__title'>
            投入资金自由选择分配账户比例
          </div>
          <div className='mine-new-page__service__earningcard__content'>
            <div className='content-text'>增加您的养老收益，点击下方按钮查看</div>
            {/* <div className='content-button' onClick={clickView}>点击查看</div> */}
            <div className='content-button-group'>
              <div className='content-button-bg'>
                <div className='content-button' onClick={clickView}>税优版养老金</div>
              </div>
              <div className='content-button-bg'>
                <div className='content-button' onClick={clickView2}>非税优养老金</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      {show ? <SearchModal onSearch={onSearch} onClose={closeModal}></SearchModal> : null}
      {showDetail ? <DetailModal data={detailData} onClose={closeDetail}></DetailModal> : null}
    </div>
  )
}

export default MineNewPage;