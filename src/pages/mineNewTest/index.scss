.mine-new-page {
  background-color: #fff;
  min-height: 100vh;
  box-sizing: border-box;

  &__header {
    margin: 0 auto;
    height: 150px;
    background-image: url('../../assets/head-bg.png');
    background-repeat: no-repeat;
    background-size: 100% 120px;
    overflow: hidden;

    &__card {
      position: relative;
      margin: 20px 13px 0 13px;
      height: 130px;
      border-radius: 10px;
      display: flex;
      flex-direction: column;
      background-image: url('../../assets/minenew/bjt.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;

      &__avatar {
        display: flex;
        align-items: center;
        margin: 28px 8px 0 24px;

        .avatar {
          width: 50px;
          height: 50px;
          border-radius: 50%;
          overflow: hidden;
          margin-right: 12px;
          background-color: #eee;

          img {
            width: 100%;
            height: 100%;
          }
        }

        .username {
          color: #8E662B;
          font-size: 18px;
          font-weight: bold;
        }
      }

      // &__info {
      //   // Placeholder for info section
      //   height: 40px;
      //   background-color: #f0f0f0;
      //   border-radius: 4px;
      //   margin-bottom: 16px;
      // }

      &__btnbox {
        position: absolute;
        top: 8px;
        right: 0px;
        margin: 16px 0 0 0;
        display: flex;
        justify-content: flex-end;


        .logout-btn {
          display: flex;
          justify-content: flex-end;
          align-items: center;
          color: #fff;
          width: 85px;
          height: 20px;
          background: #CBB190;
          border-radius: 20px 0px 0px 20px;

          .icon {
            width: 16px;
            height: 16px;
            background: linear-gradient(180deg, #EDCA9D 0%, #B59872 100%);
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
          }

          span {
            margin-right: 8px;
            font-size: 12px;
          }
        }
      }
    }
  }

  &__account {
    margin: 16px 0 0 0;

    &__area {
      margin: 14px;
      background: linear-gradient(to right, #FFE3BF 1%, #F7EEE0 49%, #F0DAC0 100%), #D4B286;
      border-radius: 10px;
    }

    &__bar {
      width: 100%;
      font-family: 'Adobe Heiti Std';
      font-size: 18px;
      color: #000000;
      padding: 0 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .my-policy {
        background: linear-gradient(to bottom, #F8F4F1, #E7D9CE);
        width: 70px;
        height: 32px;
        border-radius: 6px;
        display: flex;
        justify-content: center;
        align-items: center;

        .my-policy-bg {
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 14px;
          background: linear-gradient(to bottom, #C1722B, #79400D);
          background-clip: text;
          color: transparent;
          border-radius: 6px;
          font-weight: 700;
        }
      }
    }

    &__cardnone {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 16px;
      background-color: #fff;
      min-height: 115px;
      margin: 0 16px 16px 16px;
      background: linear-gradient(to right, #FFE3BF 1%, #F7EEE0 49%, #F0DAC0 100%), #D4B286;
      border-radius: 10px;
      color: #B06E20;
    }

    &__card {
      padding: 16px;
      background-color: #fff;
      min-height: 115px;
      background: linear-gradient(to right, #FFE3BF 1%, #F7EEE0 49%, #F0DAC0 100%), #D4B286;
      border-radius: 10px;

      &__userinfo {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 6px 0;
      }

      &__title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        .title-left {
          display: flex;
          align-items: center;
          font-size: 16px;

          .icon {
            margin: 0 0 0 8px
          }
        }

        .title-right {
          font-size: 14px;
          color: #A1711C;
          cursor: pointer;
        }
      }

      &__amount {
        margin: 0 0 12px 0;
        font-size: 24px;
      }

      &__content {
        .content-left {
          display: flex;
          align-items: center;
          font-size: 14px;
          color: #666;
          display: flex;

          .icon {
            display: flex;
            align-items: center;
            margin-left: 8px;
            color: #ff9800;
          }
        }
      }
    }

    .search-btn-area {
      width: 100%;
      padding: 16px;

      .search-btn-area__bg {
        background: linear-gradient(0deg, #E7D9CE 0%, #F8F4F1 100%);
        border-radius: 6px;
        width: 100px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;

        button {
          font-family: 'Source Han Sans-Bold', sans-serif;
          background: linear-gradient(to bottom, #C1722B, #79400D);
          background-clip: text;
          color: transparent;
          width: 100%;
          border-radius: 6px;
          font-size: 14px;
          font-weight: 900;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
        }
      }


    }

  }

  &__service {
    background-size: 100% 100%;
    border-radius: 8px;
    padding: 0 16px 16px 16px;

    &__text {

      margin: 0 16px 16px 16px;

      .title-text__bold {
        background: linear-gradient(to right, #854719 0%, #C1722B 25%, #A15C13 70%, #79400D 100%);
        background-clip: text;
        color: transparent;
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 8px;
      }

      .title-text {
        background: linear-gradient(to right, #854719 0%, #C1722B 25%, #A15C13 70%, #79400D 100%);
        background-clip: text;
        color: transparent;
        font-size: 14px;
      }
    }

    &__earningcard {
      height: 128px;
      background-image: url('../../assets/minenew/bjt.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      border-radius: 8px;
      padding: 16px;
      color: #fff;

      &__title {
        background: linear-gradient(to right, #854719 0%, #C1722B 25%, #A15C13 70%, #79400D 100%);
        font-family: 'YouSheBiaoTiYuan';
        font-size: 22px;
        font-weight: bold;
        background-clip: text;
        color: transparent;
        line-height: 24px;
        margin-bottom: 12px;
      }

      &__content {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        flex-direction: column;

        .content-text {
          font-size: 12px;
          color: #92531E;
        }

        .content-button-group {
          width: 100%;
          height: 60px;
          display: flex;
          justify-content: space-around;
          align-items: center;

          .content-button-bg {
            border-radius: 45px;
            background: linear-gradient(to bottom, #F8F4F1, #E7D9CE);
            animation: bian 0.5s ease-in-out infinite alternate;

            .content-button {
              display: flex;
              justify-content: center;
              align-items: center;
              width: 140px;
              height: 32px;
              border: 1px solid #B06E20;
              background: linear-gradient(to bottom, #C1722B, #79400D);
              background-clip: text;
              color: transparent;
              font-size: 16px;
              font-weight: 900;
              padding: 2px 8px;
              border-radius: 45px;
              cursor: pointer;
            }
          }
        }
      }
    }
  }
}

@keyframes bian {
  0% {
    transform: scale(1);
  }

  100% {
    transform: scale(0.8);
  }
}