/*
 * @Author: w<PERSON><PERSON><PERSON>
 * @Date: 2024-02-19 14:59:52
 * @LastEditors: wuqiang
 * @LastEditTime: 2024-02-20 20:18:25
 */
import React from 'react';
import { useHistory } from 'react-router';
import './index.scss';
import modal_insure from '@/assets/mine/modal_insure.png';
import icon_close from '@/assets/blue_card/ic_close.png'

export function InsureModal(props: any) {
  const history = useHistory();
  const { isInsured, setIsInsured } = props;

  return (
    <div className='insure_modal_mask' onClick={() => {
      setIsInsured(true);
    }}>
      <div className='modal_insure'>
      <img src={modal_insure} alt=''  onClick={() => {
        history.push('/poster')
      }} />
      <img src={icon_close} className='icon_close' onClick={() => {
        setIsInsured(false);
      }} />
      </div>
    </div>
  )
}