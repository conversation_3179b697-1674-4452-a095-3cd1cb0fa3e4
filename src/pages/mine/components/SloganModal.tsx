/*
 * @Author: w<PERSON><PERSON><PERSON>
 * @Date: 2024-02-19 14:59:52
 * @LastEditors: wuqiang
 * @LastEditTime: 2024-03-13 09:11:02
 */
import React from 'react';
import { useHistory } from 'react-router';
import './index.scss';
import mine_adv from '@/assets/mine_adv.png';
import icon_close from '@/assets/ic_close.png'
import { useSelector } from 'react-redux';

export default function SloganModal(props: any) {
  const history = useHistory();
  const { isShowed, setIsShowed } = props;
  const userInfo = useSelector((state: any) => state?.user)

  return (
    <div className='insure_modal_mask' onClick={() => {
      setIsShowed(false);
    }}>
      <div className='modal_insure'>
      <img src={mine_adv} alt='' onClick={() => {
        if (userInfo.insurance_user_id) {
          window.location.href = `https://wechat.crtic.com/rongjunbao/zeroDeductible/home?platformCode=rongjunbaojingxuan1&channelCode=25&channelType=3&userNo=${userInfo.insurance_user_id}`
        }
      }} />
      <img src={icon_close} className='icon_close' alt='' onClick={() => {
        setIsShowed(false);
      }} />
      </div>
      
    </div>
  )
}