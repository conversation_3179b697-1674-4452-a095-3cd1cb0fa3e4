/*
 * @Author: your name
 * @Date: 2021-09-10 16:21:51
 * @LastEditTime: 2024-12-04 21:06:45
 * @LastEditors: wuqi<PERSON>
 * @Description: In User Settings Edit
 * @FilePath: /yihaojinyuan_min/Users/<USER>/workspace/huijun-frontend/src/pages/mine/index.tsx
 */
import React, { useEffect, useRef, useState } from 'react'
import { useHistory } from 'react-router-dom'
import { Toast } from 'antd-mobile'
// 主标题：中国融通保险，现/退役军人家庭专属保障来了！

// 副标题：不限疾病/意外、不限治疗方式，0元以上可报销，报销比例可达100%
import ShareGuide from '@/components/ShareGuide'
import dayjs from 'dayjs'
import mine_bg from '@/assets/mine_bg.png'
import icon_insurance from '@/assets/icon_insurance.png'
import icon_service from '@/assets/icon_service.png'
import avatar from '@/assets/avatar.png'
import tuiyi from '@/assets/tuiyi_ad.gif'
import './index.scss'
import { useDispatch, useSelector } from 'react-redux'
import { RESET_USER_INFO } from '@/constants'
import SloganModal from './components/SloganModal'
import { getShareMaterial } from '@/utils/share'

function Header() {
  const dispatch = useDispatch()
  const history = useHistory()
  const [isShow, setIsShow] = useState(false)
  const userInfo = useSelector((state: any) => state?.user)
  const [token, setToken] = useState('')

  const handleLogin = () => {
    if (!token) {
      history.push('/commonLogin')
    }
  }

  const handleLogout = () => {
    dispatch({ type: RESET_USER_INFO })
    localStorage.clear()
    Toast.info('您已退出登录!', 2)
  }

  useEffect(() => {
    getShareMaterial({
      url: 'mine',
      title: '利好消息！退役军人家庭《终身重疾惠军版》来了！',
      text: '确诊疾病可一次性获得一笔保障金，最高可达75万元！立即获取保障！',
      imgUrl: 'https://cii.huijun365.com//slogan.jpg'
    })
  }, [])

  return (
    <div className="header" style={{ background: `url(${mine_bg}) 50% 50% / cover no-repeat` }}>
      <div className="user">
        <div
          className="avatar"
          style={{
            background: `url(${
              userInfo?.head_img ? userInfo.head_img : avatar
            }) 50% 50% / cover no-repeat`
          }}></div>

        <div className="infor">
          <span onClick={handleLogin}>
            {userInfo?.token ? (userInfo as any)?.nickname || '匿名' : '登录'}
          </span>
          {userInfo?.token && (
            <span
              onClick={handleLogout}
              className="btn_logout"
              style={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>
              退出登录 {userInfo?.user_no && <span>{userInfo?.user_no}</span>}
            </span>
          )}
        </div>
      </div>
      <div className="share">
        <span>
          {/* 2023年蓝卡办理开启 | 通知战友领福利 */}
          推荐给好友 ｜ 让战友同享福利
        </span>
        <div
          className="btnShare"
          onClick={() => {
            setIsShow(true)
            // history.push('/fmindex')
          }}>
          立即通知
        </div>
      </div>
      <ShareGuide isShow={isShow} setIsShow={setIsShow} />
    </div>
  )
}

const _gotoService = () => {
  setTimeout(() => {
    window.location.href = 'https://im1c5366d.7x24cc.com/fu_fgsff'
  }, 300)
}

function Service() {
  const history = useHistory()
  const navigateTo = url => {
    history.push(url)
  }
  return (
    <div className="service">
      <h2>我的服务</h2>
      <div className="row">
        <div className="column" onClick={() => navigateTo('/myBond')}>
          <img src={icon_insurance} className="icon" alt="" />
          <p className="name">我的保单</p>
        </div>

        {/* <div
          className="column"
          onClick={() => {
            navigateTo('/customerService')
          }}
          id="联系客服">
          <img src={icon_service} className="icon" alt="" />
          <p className="name">专属客服</p>
        </div> */}
      </div>
    </div>
  )
}

function Mine() {
  const history = useHistory();
  const [isShowed, setIsShowed] = useState(false)
  const userInfo = useSelector((state: any) => state?.user)

  return (
    <div className="mine-page">
      <Header></Header>
      <div className="content">
        <Service />
        <div className="card">
          <img onClick={() => {
            history.push('/index')
          }} src={tuiyi} alt="" id="" style={{ maxWidth: '100%' }} />
        </div>
      </div>
    </div>
  )
}

export default Mine
