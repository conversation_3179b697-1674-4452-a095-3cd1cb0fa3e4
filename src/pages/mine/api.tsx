/*
 * @Author: your name
 * @Date: 2021-09-26 13:17:16
 * @LastEditTime: 2021-12-27 11:00:29
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /blog/Users/<USER>/workspace/huijun-frontend/src/pages/myBond/api.tsx
 */
import request from '@/api/request'

class Api {
  static getBanner = (params: any): Promise<any> => 
    request(`/tongji/banner?platform=${params?.platform}`, 'POST', {}, true)

  static getUserInfo = (params: any): Promise<any> => 
    request(`/user/info`, 'POST', {}, true)
}
export default Api;