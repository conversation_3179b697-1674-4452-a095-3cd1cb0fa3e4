// #root {
//   height: 100%;
  
// }
.mine-page {
  height: 100%;
  background-color: #F4F4F4;
  overflow-y: auto;
  padding-bottom: 50px;
  // 重置轮播图样式
  .am-wingblank.am-wingblank-lg { margin-left: 0; margin-right: 0;}
  // .slider-slide { width: 253px !important; }
  h2 {
    font-size: 16px;
    color: #272A33;
    line-height: 22.5px;
    padding-left: 2px;
  }
  // 头部banner样式
  .header {
    position: relative;
    display: block;
    width: 100%;
    height: 145px !important;
    background-size: cover;
    .user {
      display: flex;
      // height: 137px;
      flex-direction: row;
      align-items: center;
      padding: 22px 20px;
      padding-left: 34px;
      padding-bottom: 7px;
      .avatar {
        position: relative;
        width: 54px;
        height: 54px;
        border-radius: 50%;
        input {
          position: absolute;
          left: 0;
          top: 0;
          right: 0;
          bottom: 0;
        }
      }
      .infor {
        flex: 1;
        display: flex;
        flex-direction: column;
        margin-left: 11px;
        color: #fff;
        font-size: 20px;
        font-weight: bold;
        cursor: pointer;
        .btn_logout {
          font-size: 14px;
          color: rgba(255, 255, 255, 0.8);
          margin-top: 5px;
          cursor: pointer;
        }
      }
    }
    .tip {
      font-size: 10px;
      color: rgba(255, 255, 255, 0.5);
      padding-left: 20px;
    }
    .share {
      display: flex;
      position: absolute;
      align-items: center;
      justify-content: space-between;
      padding: 0 13.5px;
      bottom: 0;
      left: 12px;
      right: 12px;
      height: 43px;
      background: linear-gradient(180deg, #FFF5E0 0%, #FFF0D1 100%);
      border-radius: 8px 8px 0px 0px;
      color: #B38C3E;
      font-size: 14px;
      font-weight: bold;
      .btnShare {
        display: flex;
        font-weight: normal;
        align-items: center;
        justify-content: center;
        width: 65px;
        height: 24px;
        background: linear-gradient(90deg, #F7DCA4 0%, #EDC87F 100%);
        border-radius: 12px;
        color: #B38C3E;
        font-size: 12px;
      }
    }
  }
  .content {
    box-sizing: border-box;
    width: 100%;
    padding: 12px;
    padding-bottom: 16px;
    // 我的服务栏样式
    .service {
      border-radius: 5px;
      padding: 15px 12.5px 8px;
      background-color: #fff;
      .row {
        display: flex;
        flex-direction: row;
        // justify-content: space-between;
        margin-top: 8px;
        flex-wrap: wrap;
        .column {
          position: relative;
          // margin-left: 20px;
          // flex: 1;
          width: 25%;
          text-align: center;
          margin-bottom: 10px;
          .dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            position: absolute;
            left: 65%;
            background-color: red;
          }
          .icon {
            width: 33px;
            height: 33px;
          }
          .name {
            font-size: 12px;
            color: rgba(99, 100, 119, 1);
          }
        };
        .column:first-of-type {
          margin-left: 0;
        }
      }
    }

    .card {
      margin-top: 10px;
      background-color: #fff;
      border-radius: 5px;
      // padding: 15px;
      
      h2 {
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: #272A33;
        font-size: 16px;
        line-height: 22.5px;
        padding: 0;
        margin-bottom: 12px;
        
      }
      .red_card_entry {
        width: 329px;
        height: 92.5px;
      }
      .classroom {
        width: 100%;
        max-width: 100%;
        height: 125px;
        border-radius: 4px;
      }
    }

    // 家庭成员模块样式
    .member {
      margin-top: 10px;
      border-radius: 5px;
      padding: 15px 12.5px 18px;
      background-color: #fff;
      .row {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        margin-top: 10px;
        .column {
          margin-left: 20px;
          width: 16%;
          text-align: center;
          .icon {
            width: 37px;
            height: 37px;
          }
          .name {
            font-size: 12px;
            margin-top: 8px;
          }
        };
        .column:first-of-type {
          margin-left: 0;
        }
      }
    }

    // 推荐模块样式
    .recommend {
      margin-top: 10px;
      border-radius: 5px;
      padding: 15px 12.5px 18px;
      background-color: #fff;
      overflow: hidden;
      .row {
        margin-top: 13px;
      }
      .slider-decorator-0 {
        bottom: -18px !important;
      }
      .slider-frame {
        overflow: auto;
        overflow-x: hidden;
      }
      // .column {
      //   margin-left: 10px;
      // }
    }
  }
  .am-carousel-wrap-dot > span {
    width: 7px;
    height: 2.5px;
    // background-color: #d8d8d8;
    border-radius: 1.5px;
  }
  .banner_donation {
    width: 100%;
    margin-top: 10px;
  }
}
