/*
 * @Author: your name
 * @Date: 2021-09-13 14:07:25
 * @LastEditTime: 2024-07-14 18:45:39
 * @LastEditors: wuqiang
 * @Description: In User Settings Edit
 * @FilePath: /blog/Users/<USER>/workspace/huijun-frontend/src/pages/login/index.tsx
 */
import React, { useEffect, useRef, useState } from 'react'
import { useHistory, useLocation } from 'react-router-dom'
import './index.scss'
import { isWechat } from '@/utils'
import { Wechat } from './components'

function Login() {
  const history = useHistory()
  const [isInWechat] = useState(() => {
    return isWechat()
  })

  const navigateTo = param => {
    return () => {
      // history.push(param)
      window.location.href = 'https://cii.huijun365.com/index'
    }
  }

  return (
    <div className="common-login-page">
      {isInWechat && <Wechat />}
      {!isInWechat && (
        <>
          <p className="desc_title">请在微信客户端打开此页面</p>
          <div className="desc_nav">
            <span onClick={navigateTo('/index')}>首页</span>
            <span onClick={() => history.goBack()}>上一页</span>
          </div>
        </>
      )}
    </div>
  )
}

export default Login
