.wechat-login-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  width: 100%;
  background: #fff;
  border-radius: 4px;
  line-height: 40px;
  font-size: 16px;
  cursor: pointer;
  margin-top: 80px;
  font-weight: 500;
  img {
    height: 25px;
    width: 25px;
    margin-right: 10px;
  }
  // opacity: 0.38;
}
.common-login-page {
  box-sizing: border-box;
  min-height: 100%;
  width: 100%;
  background: #fff;
  padding: 0 30px;
  position: relative;
  padding-bottom: 16px;
  .top {
    margin: 0;
  }

  .top h2 {
    font-size: 27px;
    font-weight: bold;
    color: #252935;
    line-height: 37.5px;
    height: auto;
    margin-top: 44px;
  }
  
  .top .desc {
    margin-top: 5px;
    color: #828282;
    line-height: 18.5px;
    font-size: 13px;
  }

  .input-box {
    margin-top: 40px;
  }

  .input-line {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin: 0 0 30px;
    height: 40px;
    line-height: 40px;
    border-bottom: 1px solid #e4e4e4;
    position: relative;
  }

  .input-line label {
    font-size: 16px;
    font-weight: bold;
    display: inline-block;
    width: 60px;
  }
  .input-line .input-outer {
    flex: 1;
    height: 100%;
    position: relative;
  }
  .input-line input {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    width: 100%;
    height: 100%;
    bottom: 0;
    border: none;
    font-size: 16px;
    outline: none;
    // width: 160px;
  }

  .input-line .yzm {
    position: absolute;
    right: 0;
    // border: 1px solid #ff5b56;
    padding: 0 9px;
    line-height: 24px;
    border-radius: 1000px;
    color: #ff5b56;
    font-size: 11px;
    height: 24px;
    display: inline-block;
    bottom: 9px;
    // background: #fff;
    &.card_form_code {
      box-sizing: border-box;
    }
    &.card_form_count {
      color: #CFCFCF;
      margin-right: 4px;
    }
  }

  .btn-login {
    width: 100%;
    background: #E60012;
    border-radius: 4px;
    // opacity: 0.38;
    margin-top: 50px;
  }

  .btn-login a {
    display: block;
    line-height: 40px;
    text-align: center;
    font-size: 18px;
    color: #fff;
    letter-spacing: 3px;
  }

  .login_desc {
    color: #FE6010;
    font-size: 12px;
    line-height: 16px;
    font-weight: normal;
    margin-top: -15px;
  }

  .f-box {
    background: #fff;
    border-radius: 7px;
    overflow: hidden;
    padding: 8px;
    /* margin-bottom: 1rem; */
  }

  .f-box li {
    width: 33.3333%;
    float: left;
    text-align: center;
  }

  .f-box li img {
    height: 40px;
  }

  .f-box h2 {
    font-size: 13px;
    font-weight: normal;
    margin-bottom: 12px;
  }

  .f-box ul {
    margin-bottom: 8px;
  }

  .bott {
    text-align: center;
    font-size: 10px;
  }

  .bott a {
    color: #1e64ba;
  }

  .desc_title {
    font-size: 14px;
    text-align: center;
    margin-top: 80px;
  }
  .desc_nav {
    display: flex;
    justify-content: center;
    align-items: center;
    span {
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 36px;
      font-size: 16px;
      font-weight: 500;
      margin: 20px 10px;
      width: 120px;
      border-radius: 22px;
      background-color: #f2f2f2;
      color: #06ae56;
    }
  }
}
