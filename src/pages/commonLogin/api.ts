/*
 * @message: ~
 * @Version: 1.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @since: 2025-04-21 04:20:20
 * @LastAuthor: <PERSON><PERSON><PERSON><PERSON>
 * @lastTime: 2025-07-20 11:55:25
 * @FilePath: /cii-front/src/pages/commonLogin/api.ts
 * 代码如果有bug，自求多福 😊
 */
/*
 * @Author: your name
 * @Date: 2021-09-23 18:39:14
 * @LastEditTime: 2021-11-02 14:16:32
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /cii-front/src/pages/commonLogin/api.ts
 */
import request from '@/api/request'

interface LoginParams {
  phone: string,
  code: string,
}

interface SearchParam {
  username: string, id_no: string, mobile: string
}

class Api {
  static login = (params: LoginParams): Promise<any> =>
    request('/phone/login', 'POST', params, true)

  static getCode = (params: { mobile: string, type: string }): Promise<any> =>
    request(`/user/code?mobile=${params.mobile}&type=code`, 'POST')

  static getOrderList = (params: SearchParam): Promise<any> => request(`insur_cii/api/yuanxin/order_list`, 'POST', params, true)
}
export default Api;