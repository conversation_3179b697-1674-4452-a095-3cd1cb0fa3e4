import React, { useRef, useState } from "react"
import { InputItem, Modal, Toast } from "antd-mobile"
import { validateChineseIdCard, validateChinesePhoneNumber } from "@/utils/idcard"
import "./LoginId.scss"
import Api from "./api"

const LoginId = () => {
  // const [show, setShow] = useState(false)
  const modalRef = useRef<any>(null)
  const [search, setSearch] = useState({
    username: "测试",
    id_no: "352231199512188421",
    mobile: "19121880518",
  })

  const fetchOrderList = async () => {
    const response = await Api.getOrderList({ ...search })
    console.log(response)
  }

  const submit = async () => {
    // setShow(true)
    // modalRef.current = Modal.alert('',
    //   <div className="modal-text">您还未办理退役军人家庭专属税优养老金</div>,
    //   [
    //     {
    //       text: '点击办理',
    //       style: {
    //         display: 'flex',
    //         justifyContent: 'center',
    //         alignItems: 'center',
    //         backgroundColor: '#C39E4C',
    //         height: '28px',
    //         color: '#fff',
    //         width: '100px',
    //         margin: '0 auto 10px auto',
    //         borderRadius: '8px',
    //         fontSize: '14px',
    //       },
    //       onPress: () => {
    //         // 先关闭弹窗
    //         modalRef.current?.close()
    //         // 处理关闭后的逻辑
    //         console.log('点击办理')
    //       },
    //     },
    //     // {
    //     //   text: '关闭',
    //     //   style: {
    //     //     display: 'flex',
    //     //     justifyContent: 'center',
    //     //     alignItems: 'center',
    //     //     boxSizing: 'border-box',
    //     //     border: '1px solid #C39E4C',
    //     //     height: '28px',
    //     //     color: '#C39E4C',
    //     //     width: '100px',
    //     //     margin: '0 auto 10px auto',
    //     //     borderRadius: '8px',
    //     //     fontSize: '14px',
    //     //   },
    //     //   onPress: () => {
    //     //     console.log('点击办理')
    //     //   },
    //     // }
    //   ])
    // 校验查询参数
    const isValid = validSearch()
    if (isValid) {
      await fetchOrderList()
    }
  }

  const validSearch = () => {
    if (!search.username || !search.id_no || !search.mobile) {
      Toast.show('请输入完整的信息', 1)
      return false
    }
    if (!validateChineseIdCard(search.id_no)) {
      Toast.show('身份证号码不正确', 1)
      return false
    }
    if (!validateChinesePhoneNumber(search.mobile)) {
      Toast.show('请输入正确的手机号', 1)
      return false
    }
    return true
  }

  const onChange1 = (value: string) => {
    setSearch((state) => {
      return {
        ...state,
        username: value
      }
    })
  }

  const onChange2 = (value: string) => {
    setSearch((state) => {
      return {
        ...state,
        id_no: value
      }
    })
  }

  const onChange3 = (value: string) => {
    setSearch((state) => {
      return {
        ...state,
        mobile: value
      }
    })
  }

  return (
    <div className="login-id-page">
      <div className="top">
        <div className="text">登录查询您的养老金收益</div>
        <div className="desc">请务必使用办理人（投保人）的的进行登录，若信息不一致，则无法查询到保单</div>
      </div>
      <div className="input-box">
        <div className="input-line">
          {/* <label>手机号码</label> */}
          <div className="input-outer">
            <InputItem value={search.username} className="input-in" type="text" placeholder="请输入办理人（投保人）姓名" onChange={onChange1} />
          </div>
        </div>
        <div className="input-line">
          {/* <label>验证码</label> */}
          <div className="input-outer">
            <InputItem value={search.id_no} className="input-in" type="text" placeholder="请输入办理人（投保人）身份证" onChange={onChange2} />
          </div>
        </div>
        <div className="input-line">
          {/* <label>验证码</label> */}
          <div className="input-outer">
            <InputItem value={search.mobile} className="input-in" type="text" placeholder="请输入办理人（投保人）手机号" onChange={onChange3} />
          </div>
        </div>
      </div>
      <div className="btn-login" onClick={submit}>登录</div>
      {/* <Modal
        visible={show}
        closable={true}
        popup={true}
        onClose={() => setShow(false)}
        footer={ }
      >
        <div className="modal-text">您还未办理退役军人家庭专属税优养老金</div>
      </Modal> */}
    </div >
  )
}

export default LoginId
