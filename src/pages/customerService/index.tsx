/*
 * @Author: w<PERSON><PERSON><PERSON>
 * @Date: 2024-06-24 17:47:26
 * @LastEditors: wuqi<PERSON>
 * @LastEditTime: 2024-07-11 17:02:29
 */
import React, { useEffect } from 'react'
import { useHistory } from 'react-router-dom'
import { getShareMaterial } from '@/utils/share';
import './index.scss'
import service_1 from '@/assets/service_1.png';
import service_2 from '@/assets/service_2.png';
import service_3 from '@/assets/service_3.png';
import service_4 from '@/assets/service_4.png';
import qrcode from '@/assets/qrcode.png';

const About = () => {
  const history = useHistory()

  useEffect(() => {
    getShareMaterial({
      url: 'customerService',
      title: '利好消息！退役军人家庭《终身重疾惠军版》来了！',
      text: '确诊疾病可一次性获得一笔保障金，最高可达75万元！立即获取保障！',
      imgUrl: 'https://cii.huijun365.com//slogan.jpg'
    })

  }, [])

  return (
    <div className="customer-service-page">
      <img src={service_1} alt='' />
      <img src={service_2} alt='' />
      <div className='qrcode_box'>
      <img src={service_3} alt='' />
      <img className='qrcode' src={qrcode} />
      </div>
      <img src={service_4} alt='' />
    </div>
  )
}

export default About
