.detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.2);

  display: flex;
  justify-content: flex-end;
  align-items: center;
  flex-direction: column;

  .search-container {
    width: 100%;
    background: linear-gradient(to right, #FFE3BF 1%, #F7EEE0 49%, #F0DAC0 100%, #D4B286);

    .top {
      margin: 24px 0 0 0;
      padding: 0 16px;

      .text {
        font-family: 'PingFang SC', sans-serif;
        font-weight: 600;
        font-size: 20px;
        color: #000000;
      }

      .desc {
        margin: 10px 0 0 0;
        font-family: 'PingFang SC', sans-serif;
        font-weight: 400;
        font-size: 13px;
        color: #C39E4C;
        line-height: 18px;
      }
    }

    .input-box {
      padding: 0px 16px;
      font-size: 18px;

      .input-line {
        .input-outer {
          margin: 0 0 12px 0;

          &:nth-child(1) {
            margin: 12px 0;
          }

          .input-in {
            border-radius: 8px;
            // font-size: 12px;

            input {
              font-size: 14px;
            }

          }
        }
      }
    }

    .btn-login-group {
      padding: 10px 0px;
      display: flex;
      justify-content: center;
      align-items: center;

      .btn-login {
        background-color: #C39E4C;
        margin: 6px 16px;
        width: 200px;
        height: 44px;
        border-radius: 22px;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #fff;
        font-family: 'PingFang SC', sans-serif;
        font-weight: 400;
        font-size: 14px;

        &.cancel {
          background-color: white;
          color: #000;
          border: 1px solid #C39E4C;
        }
      }
    }
  }
}

.modal-text {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 15px;
  color: #4E5969;
  line-height: 22px;
  text-align: center;
}