import { validateChineseIdCard, validateChinesePhoneNumber } from '@/utils/idcard';
import { InputItem, Toast } from 'antd-mobile';
import React, { useEffect, useRef, useState } from 'react'
import Api, { SearchResponseData } from '@/api/api';
import { Portal } from '@/components/Portal';
import './DetailModal.scss'

type SearchModalProps = {
  data: SearchResponseData | null;
  onClose: () => void;
}

const FIRST_CHARGE_TYPE = {
  1: '年交',
  2: '半年交',
  3: '季交',
  4: '月交',
  5: '趸交',
}


const DetailModal = (props: SearchModalProps) => {
  const { data, onClose } = props;
  // const [search, setSearch] = useState({
  //   username: '',
  //   id_no: '',
  //   mobile: '',
  // })
  const searchModalRef = useRef<HTMLDivElement>(null)

  const submit = async () => {
    onClose();
    // setShow(true)
    // modalRef.current = Modal.alert('',
    //   <div className="modal-text">您还未办理退役军人家庭专属税优养老金</div>,
    //   [
    //     {
    //       text: '点击办理',
    //       style: {
    //         display: 'flex',
    //         justifyContent: 'center',
    //         alignItems: 'center',
    //         backgroundColor: '#C39E4C',
    //         height: '28px',
    //         color: '#fff',
    //         width: '100px',
    //         margin: '0 auto 10px auto',
    //         borderRadius: '8px',
    //         fontSize: '14px',
    //       },
    //       onPress: () => {
    //         // 先关闭弹窗
    //         modalRef.current?.close()
    //         // 处理关闭后的逻辑
    //         console.log('点击办理')
    //       },
    //     },
    //     // {
    //     //   text: '关闭',
    //     //   style: {
    //     //     display: 'flex',
    //     //     justifyContent: 'center',
    //     //     alignItems: 'center',
    //     //     boxSizing: 'border-box',
    //     //     border: '1px solid #C39E4C',
    //     //     height: '28px',
    //     //     color: '#C39E4C',
    //     //     width: '100px',
    //     //     margin: '0 auto 10px auto',
    //     //     borderRadius: '8px',
    //     //     fontSize: '14px',
    //     //   },
    //     //   onPress: () => {
    //     //     console.log('点击办理')
    //     //   },
    //     // }
    //   ])
    // 校验查询参数
    // const isValid = validSearch()
    // if (isValid) {
    //   await fetchOrderList()
    // }
  }


  useEffect(() => {
    const clickFn = (e) => {
      if (e.target === searchModalRef.current) {
        onClose();
      }
    }
    if (searchModalRef.current) {
      window.addEventListener('click', clickFn, true)
    }
    return () => {
      window.removeEventListener('click', clickFn, true)
    }
  }, [])

  if (!data) return null;

  return (
    <Portal>
      <div className="detail-modal" ref={searchModalRef}>
        <div className='search-container'>
          <div className="top">
            <div className="text">保单详情</div>
          </div>
          <div className="input-box">
            <div className="input-line">
              <div className="input-outer">
                保单：{data.policy_code}
              </div>
            </div>
            {
              data.first_charge_type !== '5' ?
                <div className="input-line">
                  <div className="input-outer">
                    首期保费：{data.first_premium}
                  </div>
                </div> : null
            }
            <div className="input-line">
              <div className="input-outer">
                投保人：{data.applicant_name}
              </div>
            </div>
            <div className="input-line">
              <div className="input-outer">
                被保人：{data.insurant_name}
              </div>
            </div>
            <div className="input-line">
              <div className="input-outer">
                首期缴费方式：{FIRST_CHARGE_TYPE[data.first_charge_type]}
              </div>
            </div>
            <div className="input-line">
              <div className="input-outer">
                累计已交保费：{data.total_payment_amt}
              </div>
            </div>
            {/* <div className="input-line">
              <div className="input-outer">
                定期追加保费：{data.addup_amount}
              </div>
            </div> */}
            {/* <div className="input-line">
              <div className="input-outer">
                保单账户价值：{data.policy_account_value}
              </div>
            </div> */}
            <div className="input-line">
              {/* <label>验证码</label> */}
              <div className="input-outer">
                {/* <InputItem value={search.mobile} className="input-in" type="text" placeholder="请输入办理人（投保人）手机号" onChange={onChange3} /> */}
                是否税优：{data.is_tax_policy === 'Y' ? '是' : '否'}
              </div>
            </div>
          </div>
        </div >
      </div>
    </Portal >
  )
}

export default DetailModal;