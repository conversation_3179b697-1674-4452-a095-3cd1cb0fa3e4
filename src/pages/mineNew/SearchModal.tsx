import { validateChineseIdCard, validateChinesePhoneNumber } from '@/utils/idcard';
import { InputItem, Toast } from 'antd-mobile';
import React, { useEffect, useRef, useState } from 'react'
import Api, { SearchResponseDataLocal } from '@/api/api';
import { Portal } from '@/components/Portal';
import './SearchModal.scss'
// import { SEARCH_DATA } from '@/constants';


type SearchModalProps = {
  onSearch: (value: SearchResponseDataLocal[]) => void;
  onClose: () => void;
}


const SearchModal = (props: SearchModalProps) => {
  const { onSearch, onClose } = props;
  const [search, setSearch] = useState({
    username: '',
    id_no: '',
    mobile: '',
  })
  const searchModalRef = useRef<HTMLDivElement>(null)

  const fetchOrderList = async () => {
    try {
      const response = await Api.getOrderList({ ...search })
      const handleData = response.data.map(item => {
        const data = {
          applicant_name: item.applicant_name, // 投保人姓名
          applicant_idno: item.applicant_idno, // 投保人证件号
          policy_list: response.data // 保单列表
        }
        return data
      })

      // localStorage.setItem(SEARCH_DATA, JSON.stringify(search))
      onSearch(handleData)
    } catch (error) {
      Toast.info('查询失败')
    }
  }

  const submit = async () => {
    // setShow(true)
    // modalRef.current = Modal.alert('',
    //   <div className="modal-text">您还未办理退役军人家庭专属税优养老金</div>,
    //   [
    //     {
    //       text: '点击办理',
    //       style: {
    //         display: 'flex',
    //         justifyContent: 'center',
    //         alignItems: 'center',
    //         backgroundColor: '#C39E4C',
    //         height: '28px',
    //         color: '#fff',
    //         width: '100px',
    //         margin: '0 auto 10px auto',
    //         borderRadius: '8px',
    //         fontSize: '14px',
    //       },
    //       onPress: () => {
    //         // 先关闭弹窗
    //         modalRef.current?.close()
    //         // 处理关闭后的逻辑
    //         console.log('点击办理')
    //       },
    //     },
    //     // {
    //     //   text: '关闭',
    //     //   style: {
    //     //     display: 'flex',
    //     //     justifyContent: 'center',
    //     //     alignItems: 'center',
    //     //     boxSizing: 'border-box',
    //     //     border: '1px solid #C39E4C',
    //     //     height: '28px',
    //     //     color: '#C39E4C',
    //     //     width: '100px',
    //     //     margin: '0 auto 10px auto',
    //     //     borderRadius: '8px',
    //     //     fontSize: '14px',
    //     //   },
    //     //   onPress: () => {
    //     //     console.log('点击办理')
    //     //   },
    //     // }
    //   ])
    // 校验查询参数
    const isValid = validSearch()
    if (isValid) {
      await fetchOrderList()
    }
  }

  const validSearch = () => {
    if (!search.username || !search.id_no || !search.mobile) {
      Toast.show('请输入完整的信息', 1)
      return false
    }
    if (!validateChineseIdCard(search.id_no)) {
      Toast.show('身份证号码不正确', 1)
      return false
    }
    if (!validateChinesePhoneNumber(search.mobile)) {
      Toast.show('请输入正确的手机号', 1)
      return false
    }
    return true
  }

  const onChange1 = (value: string) => {
    setSearch((state) => {
      return {
        ...state,
        username: value.trim()
      }
    })
  }

  const onChange2 = (value: string) => {
    setSearch((state) => {
      return {
        ...state,
        id_no: value.trim()
      }
    })
  }

  const onChange3 = (value: string) => {
    setSearch((state) => {
      return {
        ...state,
        mobile: value.trim()
      }
    })
  }

  useEffect(() => {
    const clickFn = (e) => {
      if (e.target === searchModalRef.current) {
        onClose();
      }
    }
    if (searchModalRef.current) {
      window.addEventListener('click', clickFn, true)
    }
    return () => {
      window.removeEventListener('click', clickFn, true)
    }
  }, [])

  return (
    <Portal>
      <div className="search-modal" ref={searchModalRef}>
        <div className='search-container'>
          <div className="top">
            <div className="text">登录查询您的养老金账户</div>
            <div className="desc">请务必使用办理人（投保人）的进行登录，若信息不一致，则无法查询到保单</div>
          </div>
          <div className="input-box">
            <div className="input-line">
              {/* <label>手机号码</label> */}
              <div className="input-outer">
                <InputItem value={search.username} className="input-in" type="text" placeholder="请输入办理人（投保人）姓名" onChange={onChange1} />
              </div>
            </div>
            <div className="input-line">
              {/* <label>验证码</label> */}
              <div className="input-outer">
                <InputItem value={search.id_no} className="input-in" type="text" placeholder="请输入办理人（投保人）身份证" onChange={onChange2} />
              </div>
            </div>
            <div className="input-line">
              {/* <label>验证码</label> */}
              <div className="input-outer">
                <InputItem value={search.mobile} className="input-in" type="text" placeholder="请输入办理人（投保人）手机号" onChange={onChange3} />
              </div>
            </div>
          </div>
          <div className='btn-login-group'>
            <div className="btn-login" onClick={submit}>登录</div>
            {/* <div className="btn-login cancel" onClick={onClose}>取消</div> */}
          </div>
          {/* <Modal
        visible={show}
        closable={true}
        popup={true}
        onClose={() => setShow(false)}
        footer={ }
      >
        <div className="modal-text">您还未办理退役军人家庭专属税优养老金</div>
      </Modal> */}
        </div >
      </div>
    </Portal>
  )
}

export default SearchModal;