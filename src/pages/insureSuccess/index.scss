.insure-success-page {
  position: relative;
  width: 100%;
  padding: 0;
  padding-top: 83px;
  box-sizing: border-box;
  background: #F9F9F9;
  text-align: center;
  padding-bottom: 40px;
  .header {
    position: absolute;
    width: 100%;
    left: 0;
    top: 0;
  }
  .card_row_desc {
    font-size: 24px;
    width: 100%;
    text-align: center;
    color: red;
    font-weight: bold;
    margin-top: 40px;
    // font-family:<PERSON>, Ha<PERSON>nschweiler, 'Aria<PERSON> Narrow Bold', sans-serif
  }
  .card {
    position: relative;
    z-index: 1;
    background: url('../../assets/insureSuccess_card.png') 50% 50% / cover no-repeat;
    width: 359px;
    height: 178px;
    margin: 0 auto;
    padding: 26px 24px;
    h2 {
      font-size: 18px;
      text-align: left;
    }
    .split {
      width: 100%;
      height: 1px;
      border-bottom: 1px dashed rgba(151, 151, 151, 0.5);
      margin: 15px auto;
    }
    .card_row {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      font-size: 14px;
      margin-top: 15px;
      span {
        line-height: 18px;
        &:first-of-type {
          width: 100px;
          text-align: left;
        }
        &:nth-of-type(2) {
          flex: 1;
          text-align: right;
        }
      }
    }
  }
  .banner {
    width: 345px;
    height: 68px;
    margin: 10px auto;
  }
  .qrcode {
    width: 345px;
    padding: 15px 15px;
    box-sizing: border-box;
    background: #fff;
    border-radius: 8px;
    margin: 0 auto;
    img {
      width: 100%;
      height: auto;
    }
    
  }
}