/*
 * @Author: w<PERSON><PERSON><PERSON>
 * @Date: 2024-06-24 17:47:26
 * @LastEditors: wuqi<PERSON>
 * @LastEditTime: 2024-07-11 17:01:25
 */
import React, { useEffect } from 'react'
import { useHistory } from 'react-router-dom'
import './index.scss'
import header from '@/assets/insureSuccess_header.png'
import card from '@/assets/insureSuccess_card.png'
import banner from '@/assets/insureSuccess_banner.png'
import qrcode from '@/assets/insureSuccess_qrcode.png'
import { getShareMaterial } from '@/utils/share'

const About = () => {
  const history = useHistory()

  useEffect(() => {
    getShareMaterial({
      url: 'insureSuccess',
      title: '利好消息！退役军人家庭《终身重疾惠军版》来了！',
      text: '确诊疾病可一次性获得一笔保障金，最高可达75万元！立即获取保障！',
      imgUrl: 'https://cii.huijun365.com//slogan.jpg'
    })

  }, [])

  return (
    <div className="insure-success-page">
      <img src={header} className="header" />
      <div className="card">
        <h2>终身重疾惠军版</h2>
        <div className='split' />
        <div className='card_row_desc'>投保成功</div>
        {/* <div className='card_row'>
          <span>保障期限</span>
          <span>2022.09.09-2028.09.09</span>
        </div>
        <div className='card_row'>
          <span>被保险人</span>
          <span>张明、李美美、李丽丽、张三、张明、李美美、丽丽、张三、张伟…</span>
        </div> */}
      </div>
      <img src={banner} className="banner" />
      <div className="qrcode">
        <img src={qrcode} className="qrcode" />
      </div>
    </div>
  )
}

export default About
