/*
 * @Author: your name
 * @Date: 2021-09-18 14:05:01
 * @LastEditTime: 2024-04-09 19:14:45
 * @LastEditors: wuqiang
 * @Description: In User Settings Edit
 * @FilePath: /surpath-dray-saas/Users/<USER>/workspace/huijun-frontend/src/pages/test/index.tsx
 */
import React, { useEffect, useState } from 'react'
import { Toast, ActivityIndicator, Result } from 'antd-mobile'
import { useHistory } from 'react-router-dom'
import { isWechat } from '@/utils';
import qs from 'qs'
import './index.scss';
import { useDispatch, useSelector } from 'react-redux';
import request from '@/api/request';
import { RESET_USER_INFO, SET_USER_INFO } from '@/constants';
const params = qs.parse(window.location.search?.slice(1));
const searches = qs.stringify(params);

function BlueCardTKEntry() {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(true);
  const [showLogin, setShowLogin] = useState(false);

  const userInfo = useSelector((state: any) => state?.user)

  const getUserInfo = async () => {
    const { data, error } = await request(`/insurance_api/api/user/info`, 'POST', {}, true);
    if (!error) {
      dispatch({ type: SET_USER_INFO, value: { ...data, token: localStorage.getItem('token') } })
    } else {
      Toast.info(error || '当前同时在线人数过多，请您稍后查看!', 2);
      dispatch({ type: RESET_USER_INFO });
      localStorage.clear();
      setShowLogin(true);
      setLoading(false);
    }
  }

  useEffect(() => {
    if (localStorage.getItem('token')) {
      getUserInfo();
    } else {
      setLoading(false);
      setShowLogin(true);
    }
  }, [])

  useEffect(() => {
    if (userInfo.insurance_user_id) {
      window.location.href = `https://wechat.crtic.com/rongjunbao/zeroDeductible/home?platformCode=rongjunbaojingxuan1&channelCode=25&channelType=3&userNo=${userInfo.insurance_user_id}${searches ? '&' + searches : ''}`
    }
  }, [userInfo.insurance_user_id])

  return (
    <div className="rong-jun-bao-entry-page">

      {showLogin ? <Result
        title="温馨提示"
        message="您还没有登录"
        buttonText="点击登录"
        buttonType="primary"
        style={{ marginTop: '100px' }}
        onButtonClick={() => {
          if (isWechat()) {
            window.location.reload();
          } else {
            Toast.info('请在微信内打开!', 2000)
          }
        }}
      /> : null}

      {loading ? <ActivityIndicator
        animating={true}
        toast
        size="large"
        text="Loading..."
      /> : null}
    </div>
  )
}

export default BlueCardTKEntry
