import React, { useEffect, useState } from 'react'
import { useHistory, useLocation } from 'react-router-dom'

import Api from './api'
import { Toast } from 'antd-mobile'
import './index.scss'

import status_invalid from '@/assets/status_invalid.png'
import status_valid from '@/assets/status_valid.png'
import status_unpaid from '@/assets/status_unpaid.png'
import status_paid from '@/assets/status_paid.png'
import ic_ok from '@/assets/ic_ok.png'
import share_banner from '@/assets/share_banner.png'
import service_baodan from '@/assets/service_baodan.png'
import service_fapiao from '@/assets/service_fapiao.png'
import service_quanyi from '@/assets/service_quanyi.png'
import service_lipei from '@/assets/service_lipei.png'
import ShareGuide from '@/components/ShareGuide'
import insureFamily from '@/assets/insure_family.png'
import { getShareMaterial } from '@/utils/share'
import { useSelector } from 'react-redux'
import { bcriptName } from '@/utils'

const status = {
  invalid: {
    image: status_invalid
  },
  valid: {
    image: status_valid
  },
  unpaid: {
    image: status_unpaid
  },
  paid: {
    image: status_paid
  }
}

const Detail = () => {
  const history = useHistory()
  const location: any = useLocation()
  const [id] = useState(() => location.state?.id)
  const [detail, setDetail] = useState<any>({})
  const [isShow, setIsShow] = useState(false)
  const [isSpread, setIsSpread] = useState(false)

  const userInfo = useSelector((state: any) => state?.user)
  if (!id) {
    history.push('/myBond')
  }

  const getDetail = async () => {
    const { data, error } = await Api.getDetail({ id })
    if (error) {
      Toast.info(error)
    } else {
      setDetail(data || {})
    }
  }

  useEffect(() => {
    getDetail()
  }, [])

  useEffect(() => {
    getShareMaterial({
      url: 'detail',
      title: '利好消息！退役军人家庭《终身重疾惠军版》来了！',
      text: '确诊疾病可一次性获得一笔保障金，最高可达75万元！立即获取保障！',
      imgUrl: 'https://cii.huijun365.com//slogan.jpg'
    })

  }, [])

  return (
    <div className="detail-page">
      <div className="body">
        <div className="header">
          <h2>终身重疾惠军版</h2>
          <p>
            <span>保单号:</span>
            <span>
              {detail?.policy_no}
              </span>
          </p>
          <img src={
            status['valid'].image
            // status[detail?.status]?.image
            } alt="" />
        </div>

        <div className="desc">
          <img src={ic_ok} alt="" />
          <span>
          已支付成功，保单将从{detail?.effective_time}生效
            {/* {detail?.status === 'paid' || detail?.status === 'valid'
              ? `已支付成功，保单将从${detail?.effective_time}生效`
              : `保单已失效`} */}
          </span>
        </div>

        <img
          src={share_banner}
          className="share_banner"
          alt=""
          onClick={() => {
            setIsShow(true)
            // history.push('/fmindex')
          }}
        />

        <div className="card">
          <dl>
            <dt>保障内容</dt>
            <dd>
              <span className="prefix">被保险人</span>

              <span className="suffix">
                {detail?.insured?.name}
                {/* {bcriptName(detail?.insured?.name)} */}
                </span>
            </dd>
            <dd>
              <span className="prefix">开始时间</span>
              <span className="suffix">
                {/* 2024/06/30 */}
                {detail?.effective_time}
                </span>
            </dd>
            {/* <dd>
              <span className="prefix">结束时间</span>
              <span className="suffix">
                2025/06/30
                {detail?.expiry_time}
                </span>
            </dd> */}
            <dd>
              <div className="split" />
            </dd>
            <dd>
              <span className="prefix">保障范围</span>
              {/* <span className='suffix'>{detail?.expiry_time}</span> */}
            </dd>
            {/* <dd>
              <span className="prefix grey">保障额度 </span>
              <span className="suffix grey">300000元</span>
            </dd> */}
            <dd>
              <span className="prefix grey">重大疾病保险金基本保额100%（1次） </span>
              {/* <span className="suffix grey">300万元</span> */}
            </dd>
            <dd>
              <span className="prefix grey">中症疾病保险金基本保额60%（3次）</span>
              {/* <span className="suffix grey">100万元</span> */}
            </dd>
            <dd>
              <span className="prefix grey">轻症疾病保险金基本保额30%（5次） </span>
              {/* <span className="suffix grey">1万元</span> */}
            </dd>
            <dd>
              <span className="prefix grey">豁免轻/中/重症保费</span>
              {/* <span className="suffix grey">100万元</span> */}
            </dd>
          </dl>
        </div>

        {/* {detail?.insured?.relation === 1 ? ( */}
          <img
            src={insureFamily}
            style={{ height: 'auto' }}
            alt=""
            className="share_banner"
            onClick={() => {
              history.push('/index')
            }}
          />
        {/* ) : null} */}

        {/* {detail?.status === 'valid' || detail?.status === 'paid' || detail?.status === 'invalid' ? ( */}
          <div className="card">
            <h2>保单服务</h2>
            <div className="services">
              <div
                className="service"
                onClick={() => {
                  // if (detail?.policy_url) {
                    history.push(
                      `/viewer?path=${encodeURIComponent(
                        detail?.policy_url?.replace('http://', 'https://')
                        // 'https://cdn-insure-cii.huijun365.com/e_policy/073590182110158.pdf'
                      )}`
                    )
                  // } else {
                  //   Toast.info('电子保单正在生成中，请稍后再试！')
                  // }
                }}>
                <img src={service_baodan} className="service" alt="" />
                <span>电子保单</span>
              </div>

              <div
                className="service"
                onClick={() => {
                  // window.location.href =
                  //   'http://health.crtic.com/specialMedicine?platformCode=rongjunbaojingxuan1&productCode=P202320001301'
                }}>
                <a href='tel:**********'><img src={service_lipei} className="service" alt="" /><span>
                申请理赔</span></a>
              </div>

              {/* <div
                className="service"
                onClick={() => {
                  // window.location.href =
                  //   'https://health.crtic.com/policy?platformCode=rongjunbaojingxuan1'
                }}>
                <img src={service_quanyi} className="service" alt="" />
                <span>保单权益</span>
              </div> */}
            </div>

            
          </div>
        {/* ) : null} */}

        {/* <div className="supports">此服务由 @中国融通财产保险有限公司 提供</div> */}
      </div>
      {/* {detail?.to_be_paid_url ? (
        <div
          className="footer"
          onClick={() => {
            if (userInfo?.insurance_user_id) {
              window.location.href = detail?.to_be_paid_url
            }
          }}>
          <div className="btn">去支付</div>
        </div>
      ) : detail?.insured?.relation === 1 ? (
        <div
          className="footer"
          onClick={() => {
            if (userInfo?.insurance_user_id) {
              window.location.href = `https://wechat.crtic.com/rongjunbao/zeroDeductible/home?platformCode=rongjunbaojingxuan1&channelCode=25&channelType=3&userNo=${userInfo.insurance_user_id}`
            }
          }}>
          <div className="btn">为家人投保</div>
        </div>
      ) : null} */}

      {/* <ShareGuide isShow={isShow} setIsShow={setIsShow} /> */}
    </div>
  )
}

export default Detail
