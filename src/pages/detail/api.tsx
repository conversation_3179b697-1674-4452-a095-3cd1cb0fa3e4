/*
 * @Author: your name
 * @Date: 2021-09-26 13:17:16
 * @LastEditTime: 2024-07-11 10:09:28
 * @LastEditors: wuqiang
 * @Description: In User Settings Edit
 * @FilePath: /blog/Users/<USER>/workspace/huijun-frontend/src/pages/myBond/api.tsx
 */
import request from '@/api/request'

interface ApplyParams {
  name: string,
  idno: string,
  mobile: string,
  code: string,
  illness1: string,
  illness2: string
}

class Api {
  static getDetail = (params: any): Promise<any> => 
    request('/insur_cii/api/policy/detail', 'POST', params, true)
}
export default Api;