/*
 * @Author: w<PERSON><PERSON><PERSON>
 * @Date: 2024-02-29 00:33:45
 * @LastEditors: wuqiang
 * @LastEditTime: 2024-07-12 10:21:37
 */
.detail-page {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: linear-gradient( 180deg, rgba(233,120,135,0.06) 0%, rgba(196,3,16,0) 100%);
  background-color: #F7F8FA;

  .body {
    width: 100%;
    padding: 15px;
    box-sizing: border-box;
    flex: 1;
    overflow: auto;
    .header {
      width: 100%;
      background: linear-gradient( 180deg, #FFF3F3 0%, #FFFFFF 100%);
      border-radius: 3px;
      border: 1px solid #FFFFFF;
      box-sizing: border-box;
      position: relative;
      padding: 18px 12px;
      h2 {
        font-size: 18px;
        text-align: left;
        line-height: 25px;
        color: #272727;
        font-weight: 600;
      }
      p {
        font-size: 12px;
        line-height: 22px;
        color: #666;
        margin-top: 2.5px;
        margin-bottom: 7px;
      }
      img {
        position: absolute;
        right: 7px;
        top: 7px;
        width: 65px;
        height: 65px;
      }
    }
    .desc {
      background: url(../../assets/desc_bg.png) 50% 50% / cover no-repeat;
      display: flex;
      align-items: center;
      height: 37px;
      font-size: 12px;
      color: #7D4E37;
      img {
        width: 14px;
        height: 14px;
        margin-left: 11px;
        margin-right: 4px;
      }
    }
    .share_banner {
      width: 345px;
      height: 68px;
      margin-top: 9px;
    }
    .card {
      width: 100%;
      box-sizing: border-box;
      padding: 16px 12px;
      border-radius: 7px;
      box-shadow: inset 0px 1px 0px 0px rgba(230,230,230,0.8);
      background-color: #fff;
      margin-top: 10px;
      dl {
        width: 100%;
        dt {
          display: block;
          font-size: 16px;
          color: #474747;
          font-weight: 600;
          line-height: 22.5px;
          margin-bottom: 11px;
        }
        dd {
          display: flex;
          width: 100%;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 6px;
          line-height: 20px;
          .prefix {
            font-size: 14px;
            color: #333;
            &.grey {
              color: #999
            }
          }
          .suffix {
            font-size: 14px;
            color: #999;
            &.grey {
              color: #999
            }
          }
          .split {
            width: 100%;
            height: 1px;
            border-top: 1px solid #DEDEDE;
            margin: 5px 0;
          }
        }
      }

      .services {
        width: 96%;
        display: flex;
        // justify-content: space-between;
        margin: 15px auto;
        .service {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          line-height: 17px;
          font-size: 12px;
          color: #494949;
          margin-right: 20px;
          text-align: center;
          span, a {
            display: inline-block;
          }
          img {
            width: 42px;
            height: 42px;
            margin-bottom: 8px;
            margin-right: 0 !important;
          }
        }
      }

      
    }

    .supports {
      font-size: 12px;
      line-height: 16px;
      color: #BCBCBC;
      margin: 20px auto;
      text-align: center;
    }
  }

  .footer {
    width: 100%;
    height: 88px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    .btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 345px;
      height: 44px;
      background-color: #C40310;
      border-radius: 22px;
      color: #fff;
      font-size: 16px;
    }
  }
  
}