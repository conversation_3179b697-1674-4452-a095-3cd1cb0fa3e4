/*
 * @Author: w<PERSON><PERSON><PERSON>
 * @Date: 2024-06-24 17:47:26
 * @LastEditors: wuqi<PERSON>
 * @LastEditTime: 2024-07-11 18:54:10
 */
import request from '@/api/request'
import { Toast } from 'antd-mobile'
import React, { useEffect, useState } from 'react'
import { useHistory } from 'react-router-dom'
import './index.scss'

const About = () => {
  const history = useHistory()
  const [message, setMessage] = useState('Loading')

 

  const getPayUrl = async () => {
    const { data, error } = await request(`/insur_cii/api/policy/check_submit`, 'POST', {order_no: localStorage.getItem('order_no'), is_intelligent: 1}, true);
    if (error) {
      Toast.info(error, 2, undefined, false);
      setMessage(error);
      // history.push('/');
    } else {
      if (!data) {
        Toast.info('投保接口未返回支付地址', 2, undefined, false);
        // history.push('/');
        setMessage('投保接口未返回支付地址');
      } else {
        window.location.href = data;
      }
    }
  }

  useEffect(() => {
    if (!localStorage.getItem('token')) {
      setMessage("本地没有存储token");
    } else if (!localStorage.getItem('order_no')) {
      setMessage("本地没有存储保单号");
    } else {
      getPayUrl()
    }
    // getPayUrl();
  }, [])

  return (
    <div className="underwriting-page">
      <p>{message}</p>
    </div>
  )
}

export default About
