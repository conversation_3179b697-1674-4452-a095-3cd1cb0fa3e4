/*
 * @message: ~
 * @Version: 1.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @since: 2025-07-08 16:09:38
 * @LastAuthor: <PERSON><PERSON><PERSON><PERSON>
 * @lastTime: 2025-07-08 16:16:11
 * @FilePath: /zwapp/cii-front/src/pages/testSub/index.tsx
 * 代码如果有bug，自求多福 😊
 */
import { Button } from 'antd-mobile'
import React, { useEffect } from 'react'

const appId = 'wx47eb7cb22dd360e3'
const templateId = '1uDxHNXwYQfBmXOfPJcjAS3FynHArD8aWMEFNRGSbCc'
const url = 'https://jiadian-top.huijun365.com'

const wxLink = `https://mp.weixin.qq.com/mp/subscribemsg?action=get_confirm&appid=${appId}&scene=1000&template_id=${templateId}&redirect_url=${url}&reserved=test#wechat_redirect`

const TestSub = () => {

  const auth1 = () => {
    window.location.href = wxLink
  }

  const auth2 = () => {

  }

  useEffect(() => {

  }, [])
  return (
    <div>
      <Button type='primary' onClick={auth1}>测试授权登录1</Button>
      <Button type='primary' onClick={auth1}>测试授权登录</Button>
    </div>
  )
}

export default TestSub