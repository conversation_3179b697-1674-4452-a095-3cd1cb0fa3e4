import React, { useEffect, useState } from "react";
import {
  Picker,
  Button,
  Toast,
  InputItem
} from 'antd-mobile';
import type { CalcParam } from '@/api/api';
import Api from '@/api/api';
import './index.scss';
import AuthProvider from "@/hooks/AuthProvider";
import WrapAuthPage from "../../router/WrapAuthPage";

// 省市数据 - 联动格式
const cityData = [
  {
    label: '北京市',
    value: '北京市',
    children: [
      { label: '东城区', value: '东城区' },
      { label: '西城区', value: '西城区' },
      { label: '朝阳区', value: '朝阳区' },
      { label: '丰台区', value: '丰台区' },
      { label: '石景山区', value: '石景山区' },
      { label: '海淀区', value: '海淀区' },
    ]
  },
  {
    label: '上海市',
    value: '上海市',
    children: [
      { label: '黄浦区', value: '黄浦区' },
      { label: '徐汇区', value: '徐汇区' },
      { label: '长宁区', value: '长宁区' },
      { label: '静安区', value: '静安区' },
      { label: '普陀区', value: '普陀区' },
      { label: '虹口区', value: '虹口区' },
    ]
  },
  {
    label: '广东省',
    value: '广东省',
    children: [
      { label: '广州市', value: '广州市' },
      { label: '深圳市', value: '深圳市' },
      { label: '珠海市', value: '珠海市' },
      { label: '汕头市', value: '汕头市' },
      { label: '佛山市', value: '佛山市' },
      { label: '韶关市', value: '韶关市' },
    ]
  },
  {
    label: '江苏省',
    value: '江苏省',
    children: [
      { label: '南京市', value: '南京市' },
      { label: '无锡市', value: '无锡市' },
      { label: '徐州市', value: '徐州市' },
      { label: '常州市', value: '常州市' },
      { label: '苏州市', value: '苏州市' },
      { label: '南通市', value: '南通市' },
    ]
  },
  {
    label: '浙江省',
    value: '浙江省',
    children: [
      { label: '杭州市', value: '杭州市' },
      { label: '宁波市', value: '宁波市' },
      { label: '温州市', value: '温州市' },
      { label: '嘉兴市', value: '嘉兴市' },
      { label: '湖州市', value: '湖州市' },
      { label: '绍兴市', value: '绍兴市' },
    ]
  },
  {
    label: '山东省',
    value: '山东省',
    children: [
      { label: '济南市', value: '济南市' },
      { label: '青岛市', value: '青岛市' },
      { label: '淄博市', value: '淄博市' },
      { label: '枣庄市', value: '枣庄市' },
      { label: '东营市', value: '东营市' },
      { label: '烟台市', value: '烟台市' },
    ]
  },
  {
    label: '河南省',
    value: '河南省',
    children: [
      { label: '郑州市', value: '郑州市' },
      { label: '开封市', value: '开封市' },
      { label: '洛阳市', value: '洛阳市' },
      { label: '平顶山市', value: '平顶山市' },
      { label: '安阳市', value: '安阳市' },
      { label: '鹤壁市', value: '鹤壁市' },
    ]
  },
  {
    label: '四川省',
    value: '四川省',
    children: [
      { label: '成都市', value: '成都市' },
      { label: '自贡市', value: '自贡市' },
      { label: '攀枝花市', value: '攀枝花市' },
      { label: '泸州市', value: '泸州市' },
      { label: '德阳市', value: '德阳市' },
      { label: '绵阳市', value: '绵阳市' },
    ]
  },
  {
    label: '湖北省',
    value: '湖北省',
    children: [
      { label: '武汉市', value: '武汉市' },
      { label: '黄石市', value: '黄石市' },
      { label: '十堰市', value: '十堰市' },
      { label: '宜昌市', value: '宜昌市' },
      { label: '襄阳市', value: '襄阳市' },
      { label: '鄂州市', value: '鄂州市' },
    ]
  },
  {
    label: '湖南省',
    value: '湖南省',
    children: [
      { label: '长沙市', value: '长沙市' },
      { label: '株洲市', value: '株洲市' },
      { label: '湘潭市', value: '湘潭市' },
      { label: '衡阳市', value: '衡阳市' },
      { label: '邵阳市', value: '邵阳市' },
      { label: '岳阳市', value: '岳阳市' },
    ]
  },
];

// 缴纳年限选项
const payYearOptions = Array.from({ length: 36 }, (_, i) => ({
  label: `${i + 5}年`,
  value: i + 5
}));

// 年龄选项 (根据中国退休年龄区间：50-65岁)
const ageOptions = Array.from({ length: 16 }, (_, i) => ({
  label: `${i + 50}岁`,
  value: i + 50
}));

// 性别选项
const genderOptions = [
  { label: '男同志', value: 'male' },
  { label: '女同志', value: 'female' }
];

const CalcComputer = () => {
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<Partial<CalcParam>>({
    province: '',
    city: '',
    district: '',
    month_basic: 0,
    month_avg: 0,
    balance: 0,
    expoint: 1,
    year_count: 5,
    month_count: 139,
    result: 0
  });

  const [currentAge, setCurrentAge] = useState(60);
  const [gender, setGender] = useState('male');

  // 处理字段变化
  const handleFieldChange = (field: keyof CalcParam, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // 处理省市联动选择
  const handleCityChange = (value: string[]) => {
    const province = value[0];
    const city = value[1];
    setFormData(prev => ({
      ...prev,
      province,
      city,
      district: ''
    }));
  };

  // 处理缴纳年限选择
  const handleYearCountChange = (value: string[]) => {
    const yearCount = Number(value[0]);
    setFormData(prev => ({
      ...prev,
      year_count: yearCount
    }));
  };

  // 处理年龄选择
  const handleAgeChange = (value: string[]) => {
    const age = Number(value[0]);
    setCurrentAge(age);
  };

  // 处理性别选择
  const handleGenderChange = (value: string[]) => {
    const selectedGender = value[0];
    setGender(selectedGender);
  };

  // 计算养老金
  const calculatePension = () => {
    // 基础养老金计算公式：
    // 基础养老金 = (全省上年度在岗职工月平均工资 + 本人指数化月平均缴费工资) ÷ 2 × 缴费年限 × 1%
    const basicPension = (formData.month_avg || 0) * (1 + (formData.expoint || 1)) / 2 * (formData.year_count || 0) * 0.01;

    // 个人账户养老金计算公式：
    // 个人账户养老金 = 个人账户储存额 ÷ 计发月数
    const personalPension = (formData.balance || 0) / (formData.month_count || 139);

    // 总养老金
    const totalPension = basicPension + personalPension;

    return Math.round(totalPension);
  };

  // 提交计算
  const handleCalculate = async () => {
    try {
      setLoading(true);

      // 表单验证
      if (!formData.province || !formData.city) {
        Toast.show('请选择退休城市');
        return;
      }

      if (!formData.month_basic || formData.month_basic <= 0) {
        Toast.show('请输入有效的月缴纳基数');
        return;
      }

      if (!formData.month_avg || formData.month_avg <= 0) {
        Toast.show('请输入有效的全省上年度月平均工资');
        return;
      }

      if (!formData.balance || formData.balance <= 0) {
        Toast.show('请输入有效的账户储存额');
        return;
      }

      if (!formData.month_count || formData.month_count <= 0) {
        Toast.show('请输入有效的计发月数');
        return;
      }

      // 计算养老金
      const result = calculatePension();

      // 准备API数据
      const calcData: CalcParam = {
        province: formData.province || '',
        city: formData.city || '',
        district: formData.district || '',
        month_basic: formData.month_basic || 0,
        month_avg: formData.month_avg || 0,
        balance: formData.balance || 0,
        expoint: formData.expoint || 1,
        year_count: formData.year_count || 0,
        month_count: formData.month_count || 0,
        result: result
      };

      // 调用API保存计算结果
      await Api.calcuSave(calcData);
      Toast.show(`计算完成！预计月养老金：${result}元`);
    } catch (error) {
      console.error('计算失败:', error);
      Toast.show('计算失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <WrapAuthPage>
      <div className="calc-computer-page">
        <div className="calc-computer-form-wrap">
          <div className="calc-form">
            <div className="calc-form-content">
              {/* 退休城市选择 */}
              <div className="calc-item">
                <div className="calc-label">退休城市</div>
                <Picker
                  data={cityData}
                  value={formData.province && formData.city ? [formData.province, formData.city] : []}
                  onOk={handleCityChange}
                  cols={2}
                >
                  <div className="calc-input-item">
                    <div className="calc-picker">
                      {formData.province && formData.city
                        ? `${formData.province} ${formData.city}`
                        : '请选择城市'
                      }
                    </div>
                  </div>
                </Picker>
              </div>

              {/* 本人月缴纳基数 */}
              <div className="calc-item">
                <div className="calc-label">本人月缴纳基数<span className="icon"></span></div>
                <InputItem
                  name="month_basic"
                  type="number"
                  placeholder="请输入金额"
                  value={formData.month_basic?.toString() || ''}
                  onChange={(value) => handleFieldChange('month_basic', Number(value))}
                  extra="元"
                  className="calc-input-item"
                  style={{ textAlign: 'right', fontSize: '12px' }}
                />
              </div>

              {/* 退休时全省上年度月平均工资 */}
              <div className="calc-item">
                <div className="calc-label">退休时全省上年度月平均工资<span className="icon"></span></div>
                <InputItem
                  name="month_avg"
                  type="number"
                  placeholder="请输入金额"
                  value={formData.month_avg?.toString() || ''}
                  onChange={(value) => handleFieldChange('month_avg', Number(value))}
                  extra="元"
                  className="calc-input-item"
                  style={{ textAlign: 'right', fontSize: '12px' }}
                />
              </div>

              {/* 缴纳年限 */}
              <div className="calc-item">
                <div className="calc-label">缴纳年限<span className="icon"></span></div>
                <Picker
                  className="calc-input-item"
                  data={payYearOptions}
                  value={formData.year_count ? [formData.year_count] : [5]}
                  onOk={handleYearCountChange}
                >
                  <div className="calc-picker">
                    {formData.year_count ? `${formData.year_count}年` : '5年'}
                  </div>
                </Picker>
              </div>

              {/* 退休时账户储存额 */}
              <div className="calc-item">
                <div className="calc-label">退休时账户储存额<span className="icon"></span></div>
                <InputItem
                  name="balance"
                  type="number"
                  placeholder="请输入账户存储额"
                  value={formData.balance?.toString() || ''}
                  onChange={(value) => handleFieldChange('balance', Number(value))}
                  extra="元"
                  style={{ textAlign: 'right', fontSize: '12px' }}
                  className="calc-input-item"
                />
              </div>

              {/* 计发月数 */}
              <div className="calc-item">
                <div className="calc-label">计发月数<span className="icon"></span></div>
                <InputItem
                  name="month_count"
                  type="number"
                  placeholder="请输入计发月数"
                  value={formData.month_count?.toString() || ''}
                  onChange={(value) => handleFieldChange('month_count', Number(value))}
                  className="calc-input-item"
                  style={{ textAlign: 'right', fontSize: '12px' }}
                />
              </div>

              {/* 目前年龄 */}
              <div className="calc-item">
                <div className="calc-label">目前年龄</div>
                <Picker
                  className="calc-input-item"
                  data={ageOptions}
                  value={[currentAge]}
                  onOk={handleAgeChange}
                >
                  <div className="calc-picker">
                    {currentAge}岁
                  </div>
                </Picker>
              </div>

              {/* 选择身份 */}
              <div className="calc-item">
                <div className="calc-label">选择身份</div>
                <Picker
                  className="calc-input-item"
                  data={genderOptions}
                  value={[gender]}
                  onOk={handleGenderChange}
                >
                  <div className="calc-picker">
                    {gender === 'male' ? '男同志' : '女同志'}
                  </div>
                </Picker>
              </div>

              {/* 计算按钮 */}
              <div className="calc-button-wrapper">
                <Button
                  type="primary"
                  size="large"
                  loading={loading}
                  onClick={handleCalculate}
                  className="calc-button"
                >
                  计算结果
                </Button>
              </div>
            </div>
          </div>
        </div>
        <div className="desc">
          <div className="desc-title">▍养老金计算公式参考</div>
          <div className="desc-content">
            <p>养老金=基础养老金+个人账户养老金</p>
            <p>基础养老金=(全省上年度在岗职工月平均工资+本人指数化月平均缴费工资):2x缴费年限x1%</p>
            <p>本人指数化月平均缴费工资=全省上年度在岗职工月平均工资x本人平均缴费指数</p>
            <p>个人账户养老金=个人账户储存额:计发月数</p>
          </div>
        </div>
      </div>
    </WrapAuthPage>
  );
};

export default CalcComputer;
