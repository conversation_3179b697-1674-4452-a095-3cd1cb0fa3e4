.calc-computer-page {
  background: url('~@/assets/computer/bg.png');
  background-size: cover;
  background-repeat: no-repeat;
  min-height: 100vh;
  // background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  // padding: 16px;
  box-sizing: border-box;
  overflow: auto;

  .calc-computer-form-wrap {
    margin: 160px 0 0 0;
    padding: 12px;

    .calc-form {
      background: #fff;
      padding: 10px 20px;
      border-radius: 12px;
      box-shadow: 1px 4px 6px #4990F7 inset, 1px -4px 6px #4990F7 inset;
      max-width: 400px;

      .calc-title {
        text-align: center;
        font-size: 24px;
        font-weight: bold;
        color: #333;
        margin-bottom: 32px;
        position: relative;

        &::after {
          content: '';
          position: absolute;
          bottom: -8px;
          left: 50%;
          transform: translateX(-50%);
          width: 60px;
          height: 3px;
          background: linear-gradient(90deg, #667eea, #764ba2);
          border-radius: 2px;
        }
      }

      .calc-form-content {
        padding: 12px 0;

        .calc-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-bottom: 1px solid #eee;
          padding: 0 0 8px 0;
          margin-bottom: 12px;

          .calc-label {
            flex: 0 0 220px;
            font-size: 14px;
            font-weight: 900;
            color: #333;
            display: flex;
            align-items: center;

            .icon {
              display: inline-block;
              width: 18px;
              height: 18px;
              background: url('~@/assets/computer/mark.png') no-repeat;
              background-size: contain;
              margin-left: 4px;
            }

            // &::before {
            //   content: '*';
            //   color: #ff4d4f;
            //   margin-right: 4px;
            //   font-weight: bold;
            // }
          }

          .calc-input-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
          }

          .calc-input-item {
            border: none !important;
            background: transparent !important;
            padding: 0 !important;
            width: 100%;
            font-size: 12px;

            .am-list-line {
              padding: 0 !important;
            }

            .am-input-item {
              border: none !important;
              background: transparent !important;
              padding: 8px 0 !important;
              min-height: 24px;
              display: flex !important;
              justify-content: flex-end !important;
              align-items: center !important;

              .am-input-label {
                display: none !important;
              }

              .am-input-control {
                font-size: 13px !important;
                text-align: right !important;
                flex: 1 !important;
                display: flex !important;
                justify-content: flex-end !important;

                input {
                  font-size: 12px !important;
                  border: none !important;
                  background: transparent !important;
                  text-align: right !important;
                  width: 100% !important;

                  &::placeholder {
                    color: #bfbfbf !important;
                    text-align: right !important;
                  }
                }
              }

              .am-input-extra {
                color: #666 !important;
                font-size: 13px !important;
                padding-left: 8px !important;
                flex-shrink: 0 !important;
              }
            }
          }

          .calc-picker {
            padding: 8px 0;
            border: none;
            background: transparent;
            font-size: 13px;
            color: #333;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: space-between;
            min-height: 36px;
            box-sizing: border-box;

            &::after {
              content: '▼';
              font-size: 10px;
              color: #999;
              transform: scale(0.8);
              margin-left: 8px;
            }

            &:hover {
              color: #667eea;
            }

            &:active {
              color: #5a67d8;
            }
          }
        }

        .calc-button-wrapper {
          display: flex;
          justify-content: center;
          margin-top: 32px;
          text-align: center;

          .calc-button {
            width: 198px;
            height: 36px;
            font-size: 16px;
            font-weight: bold;
            background: linear-gradient(-28deg, #4976F3, #4990F7);
            border: none;
            border-radius: 24px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
            display: flex;
            justify-content: center;
            align-items: center;
            letter-spacing: 4px;

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
            }

            &:active {
              transform: translateY(0);
              box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
            }

            &.am-button-loading {
              opacity: 0.7;
              cursor: not-allowed;
            }
          }
        }
      }
    }
  }

  .desc {
    color: #353F5C;
    background-color: #eee;
    padding: 12px 25px;
    font-size: 12px;
    line-height: 22px;
  }
}