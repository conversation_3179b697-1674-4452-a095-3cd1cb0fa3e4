/*
 * @Author: your name
 * @Date: 2021-09-22 10:46:24
 * @LastEditTime: 2024-07-11 10:10:07
 * @LastEditors: wuqiang
 * @Description: In User Settings Edit
 * @FilePath: /yihaojinyuan_min/Users/<USER>/workspace/huijun-frontend/src/pages/myBond/components.tsx
 */
import React, { useEffect, useState } from 'react'
import { Toast } from 'antd-mobile'
import './index.scss'
import status_invalid from '@/assets/status_invalid.png'
import status_valid from '@/assets/status_valid.png'
import status_paid from '@/assets/status_paid.png'
import status_unpaid from '@/assets/status_unpaid.png'
import { useHistory } from 'react-router'
import dayjs from 'dayjs'
import { bcriptName } from '@/utils'

const status = {
  invalid: {
    image: status_invalid
  },
  valid: {
    image: status_valid
  },
  paid: {
    image: status_paid
  },
  unpaid: {
    image: status_unpaid
  }
}

function MyBondCard(props) {
  const history = useHistory()
  const { data } = props

  return (
    <div
      className={`my_bond_card ${data?.status === 'invalid' ? 'invalid' : ''}`}
      onClick={() => {
        // if (['invalid', 'valid', 'paid', 'all'].includes(data?.status)) {
          history.push('/detail', { id: data.id })
        // }
      }}>
      <div className="my_bond_card_body">
        <div className="my_bond_content">
          <h2 className="my_bond_title">
            <span>终身重疾惠军版</span>
            <span className="tag">重疾险</span>
          </h2>
          <p className="my_bond_text">{`投保人: ${bcriptName(data?.name)}`}</p>
          <p className="my_bond_text">{`生效时间: ${data?.effective_time}`}</p>
          {/* <p className="my_bond_text">{`到期时间: ${data?.expiry_time}`}</p> */}
        </div>

        {/* <img src={status[data.status]?.image} alt="" /> */}
      </div>
      {data?.status === 'unpaid' && data?.to_be_paid_url ? (
        <div
          onClick={e => {
            e.stopPropagation();
            window.location.href = data.to_be_paid_url
          }}
          className="btn_pay">
          去支付
        </div>
      ) : null}
    </div>
  )
}

export { MyBondCard }
