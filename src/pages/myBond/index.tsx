/*
 * @Author: your name
 * @Date: 2021-09-18 17:05:26
 * @LastEditTime: 2024-07-11 17:01:52
 * @LastEditors: wuqiang
 * @Description: In User Settings Edit
 * @FilePath: /blog/Users/<USER>/workspace/huijun-frontend/src/pages/myBond/index.tsx
 */
import React, { useState, useEffect } from 'react'
import CardNoData from '@/components/CardNoData/index';
import { MyBondCard } from './components'
import Api from './api'
import './index.scss'
import { useHistory } from 'react-router-dom'
import { useSelector } from 'react-redux';
import { getShareMaterial } from '@/utils/share';

function MyBond() {
  const history = useHistory()
  const [list, setList] = useState<any[] | null>([
    // {status: 'valid', name: '马玉梅', effective_time: '2024-05-23'},
    // {status: 'paid', name: '张旭东', effective_time: '2024-05-23'},
    // {status: 'invalid', name: '张旭东', effective_time: '2024-05-23'}
  ])
  const [text, setText] = useState('数据加载中...')
  const [isLoading, setIsLoading] = useState(true);
  const userInfo = useSelector((state: any) => state?.user)

  const getList = async () => {
    const { data, error } = await Api.getList({status: 'all'});

    if (data && data?.policy_list) {
      setList(data.policy_list || [])
    } else {
      setText(error || '暂无数据')
    }
  }

  useEffect(() => {

  if (!userInfo?.token) {
    history.push({ pathname: '/mine' });
  } else {
    getList()
  }
  }, [])

  useEffect(() => {
    getShareMaterial({
      url: 'myBond',
      title: '利好消息！退役军人家庭《终身重疾惠军版》来了！',
      text: '确诊疾病可一次性获得一笔保障金，最高可达75万元！立即获取保障！',
      imgUrl: 'https://cii.huijun365.com//slogan.jpg'
    })

  }, [])

  return (
    <div className="my-bond-page" style={{ display: list?.length ? '' : 'flex' }}>
      {list && list.length ? (
        <>
          {list.map((item, index) => {
            return <MyBondCard key={index} data={item} />
          })}
        </>
      ) : (
        <CardNoData text={text} list={list} />
      )}
    </div>
  )
}

export default MyBond
