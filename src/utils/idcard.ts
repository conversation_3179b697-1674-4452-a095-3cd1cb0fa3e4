export function validateChineseIdCard(idCard: string): boolean {
  // 基本格式校验
  if (!/^\d{17}[\dXx]$/.test(idCard)) {
    return false;
  }

  // 前17位数字
  const digits = idCard.substring(0, 17).split('').map(Number);
  // 校验码
  const checkCode = idCard[17].toUpperCase();

  // 加权因子
  const weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
  // 校验码对应值
  const checkCodes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];

  // 计算加权和
  let sum = 0;
  for (let i = 0; i < 17; i++) {
    sum += digits[i] * weights[i];
  }

  // 计算校验码
  const calculatedCheckCode = checkCodes[sum % 11];

  // 验证校验码
  return calculatedCheckCode === checkCode;
}

export function validateChinesePhoneNumber(phoneNumber: string): boolean {
  // 去除所有非数字字符
  const cleaned = phoneNumber.replace(/\D/g, '');

  // 验证手机号长度和格式
  return /^(?:(?:\+|00)86)?1[3-9]\d{9}$/.test(cleaned);
}


// 中国移动：134(0 - 8)、135、136、137、138、139、1440、147、148、150、151、152、157、158、159、172、178、182、183、184、187、188、195、197、198

// 中国联通：130、131、132、140(0 - 1)、145、146、155、156、166、167、175、176、185、186、196

// 中国电信：133、1349、1410、149、153、162、173、174(0 - 5)、177、180、181、189、190、191、193、199

// 虚拟运营商：165、170(0 - 2)、170(4 - 5)、171

export function preciseValidateChinesePhoneNumber(phoneNumber: string): boolean {
  const cleaned = phoneNumber.replace(/\D/g, '');

  // 精确匹配所有有效号段
  return /^(?:(?:\+|00)86)?1(?:(?:3[\d])|(?:4[5-79])|(?:5[0-35-9])|(?:6[5-7])|(?:7[0-8])|(?:8[\d])|(?:9[189]))\d{8}$/.test(cleaned);
}