/*
 * @message: ~
 * @Version: 1.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @since: 2025-06-26 23:02:08
 * @LastAuthor: <PERSON><PERSON><PERSON><PERSON>
 * @lastTime: 2025-06-26 23:10:20
 * @FilePath: /cii-front/src/utils/clickReport.ts
 * 代码如果有bug，自求多福 😊
 */
import qs from 'qs';
import { v4 as uuidv4 } from 'uuid';
import getQuery from '@/utils/query';

// 埋点上报地址
export const reportUrl = process.env.NODE_ENV === 'production' ? 'https://front-log.huijun365.com/log.js?' : 'https://front-log.huijun365.com/log.js?';

export default async function ClickReport(desc: string) {
  const query = getQuery() as unknown as any;
  try {
    const deviceInfor = {
      from: query?.report_from || '',
      source: window.location?.pathname || '',
      timestamp: +new Date(),
      requestid: uuidv4(),
      action: 'click',
      desc
    };
    await fetch(reportUrl + qs.stringify(deviceInfor));
  } catch { }
}
