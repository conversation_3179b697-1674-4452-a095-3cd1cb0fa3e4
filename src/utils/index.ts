/*
 * @Author: w<PERSON><PERSON><PERSON>
 * @Date: 2024-02-26 14:08:49
 * @LastEditors: wuqiang
 * @LastEditTime: 2024-07-11 00:54:12
 */

import dayjs from 'dayjs';

export const IDCARD_REGEXP = /^([1-6][1-9]|50)\d{4}(18|19|20)\d{2}((0[1-9])|10|11|12)(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
export const PHONE_REGEXP = /^1[3-9]\d{9}$/;
export const EMAIL_REGEXP = /^([A-Za-z0-9_\-\.])+\@([A-Za-z0-9_\-\.])+\.([A-Za-z]{2,4})$/;


export function isWechat() {
  // 根据平台信息获取筛选下载地址
  let ua: any = navigator.userAgent.toLowerCase();
  if (ua.match(/MicroMessenger/i) == 'micromessenger') {
    return true
  }
  return false
}

export function bcriptName(name) {
  try {
    if (!name?.length) {
      return '匿名'
    } else if (name?.length === 1) {
      return name;
    } else if (name?.length === 2 ) {
      return name.substr(0, 1) + '*';
    } else {
      return name.substr(0, 1).padEnd(name.length - 1, '*') + name.slice(-1);
    }
  } catch (e) {
    return name;
  }
}

export function isValidIdCard(param) {
  return IDCARD_REGEXP.test(param);
}

export function getAge(identifyNumber, time?: number) {
  if(isValidIdCard(identifyNumber)) {
    let year = parseInt(identifyNumber.substr(6, 4));
    let month = parseInt(identifyNumber.substr(10, 2));
    let date = parseInt(identifyNumber.substr(12, 2));
    let _time = time ? new Date(time) : new Date();
    let age = _time.getFullYear() - year;
    let _curMonth = _time.getMonth() + 1;
    let _curDate = _time.getDate();

    if(_curMonth < month) {
      --age;
    } else if(_curMonth === month) {
      if(_curDate < date) {
        --age
      }
    }
    return age;
  } else {
    return null
  }
}

export function getAgeByBirthDay(birthday: string) {
  let _birthday = birthday.split('-');
    let year = parseInt(_birthday[0]);
    let month = parseInt(_birthday[1]);
    let date = parseInt(_birthday[2]);
    let _time = new Date();
    let age = _time.getFullYear() - year;
    let _curMonth = _time.getMonth() + 1;
    let _curDate = _time.getDate();

    if(_curMonth < month) {
      --age;
    } else if(_curMonth === month) {
      if(_curDate < date) {
        --age
      }
    }
    return age;
}

 // 投保人年龄必须大于等于18周岁
// 为本人投保，投保年龄应大于等于18周岁小于等于55周岁
// 被保人年龄必须小于55周岁 大于28天
export function isValidApplicantAge(idCardNumber) {
  const birthDate = dayjs(idCardNumber.substring(6, 14), 'YYYYMMDD').format('YYYY-MM-DD');
 
  // 计算当前年龄
  const currentAge = dayjs().diff(birthDate, 'year');
    
  // 判断年龄是否满足条件
  const isAgeValid = currentAge >= 18;
  return isAgeValid;
}

export function isValidSelfAge(idCardNumber) {
  const birthDate = dayjs(idCardNumber.substring(6, 14), 'YYYYMMDD').format('YYYY-MM-DD');
 
  // 计算当前年龄
  const currentAge = dayjs().diff(birthDate, 'year');
    
  // 判断年龄是否满足条件
  const isAgeValid = currentAge >= 18 && currentAge <= 55;
  return isAgeValid;
}

export function isValidInsured(idCardNumber) {
  const birthday = idCardNumber.substring(6, 14);
  const now = dayjs();
  const birthdayDayjs = dayjs(birthday, 'YYYYMMDD');
  const age = now.diff(birthdayDayjs, 'year');
 
  // 判断年龄是否大于28天且小于55周岁
  const ageInDays = now.diff(birthdayDayjs, 'day');
  const ageInYears = now.diff(birthdayDayjs, 'year');
  return ageInDays >= 28 && ageInYears <= 55;
}


export function getGender(idCardNumber) {
  if (idCardNumber?.length === 18) {
    var genderDigit = parseInt(idCardNumber.charAt(16));
    if (genderDigit % 2 === 0) {
      return "F";
    } else {
      return "M";
    }
  } else if (idCardNumber?.length === 15) {
    var genderDigit = parseInt(idCardNumber.charAt(14));
    if (genderDigit % 2 === 0) {
      return "F";
    } else {
      return "M";
    }
  } else {
    return "无效的身份证号";
  }
}


export function isValidName(param) {
  return param && param.trim() && param.trim().length >= 2;
}

export function isValidEmail(param) {
  return EMAIL_REGEXP.test(param);
}

export function isValidPhone(param) {
  return PHONE_REGEXP.test(param);
}
