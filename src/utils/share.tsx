/*
 * @Author: your name
 * @Date: 2021-09-28 15:11:09
 * @LastEditTime: 2024-07-07 10:48:52
 * @LastEditors: wuqiang
 * @Description: In User Settings Edit
 * @FilePath: /cii-front/src/utils/share.tsx
 */
// import $ from 'jquery';
// import { httpRequest, showAlert } from '@utils/index';
// import { LOGO } from '@constants/index';
import * as qs from 'qs'
declare let wx: any
const appId = 'wx47eb7cb22dd360e3'
const BaseShareUri = window.location.origin + ''

const getShareUrl = () => {
  let path = window.location.pathname
  switch (path) {
    case '/bgPage':
    case '/mine':
    case '/shuiyou':
      return `${BaseShareUri}${'/bgPage'}`
    case '/hospital':
      return `${BaseShareUri}${'/rongjunbaoEntry'}`
    default:
      return `${BaseShareUri}${'/customerService'}`
  }
}

export const getShareMaterial = async params => {
  let url = window.location.href.split('#')[0]
  const { title, imgUrl, text, searches } = params
  await fetch(`${BaseShareUri}/insur_cii/api/user/share?url=${encodeURIComponent(url)}`, {
    method: 'get'
  })
    .then(res => res.json())
    .then(res => {
      const { code, data } = res || {}
      if (code === 0 && data) {
        const { timestamp, nonce_str: nonceStr, signature, jsapi_ticket: ticket } = data
        console.log('data =>', data);
        wx.config({
          // debug: true,
          appId,
          timestamp,
          nonceStr,
          signature,
          jsApiList: ['updateAppMessageShareData', 'updateTimelineShareData'],
          openTagList: ['wx-open-launch-weapp']
        })

        wx.ready(function () {
          wx.updateTimelineShareData({
            title: title, // 分享标题
            desc: text,
            link: getShareUrl() + (searches ? '?' + qs.stringify(searches) : ''), // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
            imgUrl: imgUrl, // 分享图标
            success: function () {
              // 用户确认分享后执行的回调函数
            },
            cancel: function () {
              // 用户取消分享后执行的回调函数
            }
          })
          wx.updateAppMessageShareData({
            title: title, // 分享标题
            desc: text, // 分享描述
            link: getShareUrl() + (searches ? '?' + qs.stringify(searches) : ''), // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
            imgUrl: imgUrl, // 分享图标
            type: '', // 分享类型,music、video或link，不填默认为link
            dataUrl: '', // 如果type是music或video，则要提供数据链接，默认为空
            success: function () {
              // 用户确认分享后执行的回调函数
            },
            cancel: function () {
              // 用户取消分享后执行的回调函数
            }
          })
        })
      }
    })
    .catch(e => {
      console.log(e)
    })
}
