/*
 * @Author: w<PERSON><PERSON><PERSON>
 * @Date: 2023-08-08 09:15:28
 * @LastEditors: wuqiang
 * @LastEditTime: 2024-07-28 09:07:53
 */
import { lazy } from 'react'
import { withRouter } from 'react-router-dom'
import WrapAuthPage from './WrapAuthPage'
const Index = lazy(() => import(/* webpackChunkName: "Index" */ '@/pages/index'))
const CommonLogin = lazy(() => import(/* webpackChunkName: "About" */ '@/pages/commonLogin'))
const LoginId = lazy(() => import(/* webpackChunkName: "About" */ '@/pages/commonLogin/loginId'))
const BgPage = lazy(() => import(/* webpackChunkName: "About" */ '@/pages/bgPage'))
const Shuiyou = lazy(() => import(/* webpackChunkName: "About" */ '@/pages/shuiyou'))
const Mine = lazy(() => import(/* webpackChunkName: "Detail" */ '@/pages/mine'))
const MineNew = lazy(() => import(/* webpackChunkName: "Detail" */ '@/pages/mineNew'))
const MineNewTest = lazy(() => import(/* webpackChunkName: "Detail" */ '@/pages/mineNewTest'))
const MyBond = lazy(() => import(/* webpackChunkName: "Detail" */ '@/pages/myBond'))
const Detail = lazy(() => import(/* webpackChunkName: "Detail" */ '@/pages/detail'))
const RongJunBaoEntry = lazy(() => import(/* webpackChunkName: "Detail" */ '@/pages/rongjunbaoEntry'))
const PdfViewer = lazy(() => import(/* webpackChunkName: "Detail" */ '@/pages/viewer'))
const CustomerService = lazy(() => import(/* webpackChunkName: "Detail" */ '@/pages/customerService'))
const InsureSuccess = lazy(() => import(/* webpackChunkName: "Detail" */ '@/pages/insureSuccess'))
const Test = lazy(() => import(/* webpackChunkName: "Detail" */ '@/pages/test'))
const Underwriting = lazy(() => import(/* webpackChunkName: "Detail" */ '@/pages/underwriting'))
const Caculator = lazy(() => import(/* webpackChunkName: "Detail" */ '@/pages/caculator'))
const CaculatorNew = lazy(() => import(/* webpackChunkName: "Detail" */ '@/pages/caculatorN'))
const appClose = lazy(() => import(/* webpackChunkName: "Detail" */ '@/pages/appClose'))

const CalcComputer = lazy(() => import(/* webpackChunkName: "Detail" */ '@/pages/calcComputer'))
const CalcResult = lazy(() => import(/* webpackChunkName: "Detail" */ '@/pages/calcResult'))

export interface RouteConfig {
  path: string
  component?: any
  exact?: boolean,
  meta?: any
}

export const routes: RouteConfig[] = [
  // {
  //   path: '/viewer',
  //   component: PdfViewer,
  //   exact: true
  // },
  // {
  //   path: '/caculator',
  //   component: Caculator,
  //   exact: true
  // },
  // {
  //   path: '/caculatorNew',
  //   component: CaculatorNew,
  //   exact: true
  // },
  // {
  //   path: '/test',
  //   component: Test,
  //   exact: true
  // },
  // {
  //   path: '/underwriting',
  //   component: Underwriting,
  //   exact: true
  // },
  // {
  //   path: '/commonLogin',
  //   component: CommonLogin,
  //   exact: true
  // },
  {
    path: '/loginid',
    component: LoginId,
    exact: true,
    meta: {
      title: '登录'
    }
  },
  // {
  //   path: '/index',
  //   component: Index,
  //   exact: true
  // },
  // {
  //   path: '/insureSuccess',
  //   component: InsureSuccess,
  //   exact: true
  // },
  // {
  //   path: '/rongjunbaoEntry',
  //   component: RongJunBaoEntry,
  //   exact: true
  // },
  {
    path: '/mine',
    component: MineNew,
    exact: true,
    meta: {
      title: '个人中心',
    }
  },
  {
    path: '/minetest',
    component: MineNewTest,
    exact: true
  },
  // {
  //   path: '/mineNew',
  //   component: MineNew,
  //   exact: true
  // },
  {
    path: '/bgPage',
    component: BgPage,
    exact: true
  },
  {
    path: '/shuiyou',
    component: Shuiyou,
    exact: true
  },
  {
    path: '/computer',
    component: WrapAuthPage(CalcComputer),
    exact: true,
    meta: {
      auth: true
    }
  },
  {
    path: '/computerResult',
    component: WrapAuthPage(CalcResult),
    exact: true,
    meta: {
      auth: true
    }
  },


  // {
  //   path: '/detail',
  //   component: Detail,
  //   exact: true
  // },
  // {
  //   path: '/myBond',
  //   component: MyBond,
  //   exact: true
  // },
  // {
  //   path: '/customerService',
  //   component: CustomerService,
  //   exact: true,
  //   meta: {
  //     title: '联系客服'
  //   }
  // },
  // {
  //   path: '*',
  //   component: Index,
  //   exact: true
  // },
  {
    path: '/index',
    component: appClose,
    exact: true
  },
  {
    path: '*',
    component: appClose,
    exact: true
  },
]
