/*
 * @Author: w<PERSON>qi<PERSON>
 * @Date: 2023-08-08 09:15:28
 * @LastEditors: wuqiang
 * @LastEditTime: 2024-09-24 10:59:29
 */
import React, { Suspense } from 'react'
import { BrowserRouter, Route, Redirect, Switch } from 'react-router-dom'
import { routes } from './routes'
import AppTabBar from '@/components/tabbar'
import { LoadingElement } from '@/components/loading'
import BuryPoint from '@/components/BuryPoint'
import BlueCardEndModal from '@/components/BlueCardEndModal'

/* Use components to define routes */
const RouterView = () => (
  <BrowserRouter basename="/">
    <BuryPoint />
    {/* <AppTabBar></AppTabBar> */}
    <Suspense fallback={LoadingElement}>
      <Switch>
        {routes.map(route => (
          <Route key={route.path} path={route.path} component={
            route.component} exact={route.exact}></Route>
        ))}
        <Redirect to="/index"></Redirect>
        {/* <Redirect to="/mine"></Redirect> */}
      </Switch>
    </Suspense>

  </BrowserRouter>
)
export default RouterView
