/*
 * @Author: w<PERSON><PERSON><PERSON>
 * @Date: 2023-08-08 09:15:28
 * @LastEditors: wuqiang
 * @LastEditTime: 2024-07-08 17:11:02
 */
import React from 'react'
import ReactDOM from 'react-dom'
import './index.scss'
import App from './App'
// import AppClose from './AppClose'
import * as serviceWorker from './serviceWorker'
import './utils/rem'

import { Provider } from 'react-redux'
import store from './store'

ReactDOM.render(
  // <React.StrictMode>
  <Provider store={store}>
    <App />
    {/* <AppClose /> */}
  </Provider>,
  // </React.StrictMode>,
  document.getElementById('root')
)

// If you want your app to work offline and load faster, you can change
// unregister() to register() below. Note this comes with some pitfalls.
// Learn more about service workers: https://bit.ly/CRA-PWA
serviceWorker.unregister()
