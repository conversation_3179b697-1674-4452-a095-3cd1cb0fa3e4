/*
 * @Author: your name
 * @Date: 2021-12-13 14:46:44
 * @LastEditTime: 2024-07-18 15:35:42
 * @LastEditors: wuqiang
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: /blog/Users/<USER>/workspace/huijun-frontend/src/components/BuryPoint/index.tsx
 */
import React, { SyntheticEvent, useEffect, useState } from 'react'
import { useLocation, useHistory } from 'react-router-dom'
import { useDispatch, useSelector } from 'react-redux'
import qs from 'qs'
import FingerprintJS from '@fingerprintjs/fingerprintjs'
import { v4 as uuidv4 } from 'uuid'
const SET_DEVICE_INFOR = 'SET_DEVICE_INFOR';
type PageStateProps = {
  deviceInfor: any
}

const BuryPoint = props => {
  const location: any = useLocation()
  const history = useHistory()
  const dispatch = useDispatch()
  const deviceInfor = useSelector((state: PageStateProps) => state?.deviceInfor)

  useEffect(() => {
    window.addEventListener('click', (e: any) => {
      let id = e?.target?.id || e?.target?.parentNode?.id;
      if(id) {
        dispatch({
          type: SET_DEVICE_INFOR,
          value: {
            // isBuryPoint: true,
            from: location?.state?.from || '',
            source: location?.pathname || '',
            timestamp: +new Date(),
            requestid: uuidv4(),
            action: 'click',
            desc: id
          }
        })
      }
    })
  }, [])

  const getDeviceInfor = async () => {
    let channel = (location.search && qs.parse(location.search?.slice(1))?.channel) || location?.state?.channel || '';
    let deptCode = (location.search && qs.parse(location.search?.slice(1))?.deptCode) || location?.state?.deptCode || '';
    console.log('channel =>', channel);
    try {
      const fpPromise = FingerprintJS.load()
      const fp = await fpPromise
      const result = await fp.get()
      dispatch({
        type: SET_DEVICE_INFOR,
        value: {
          // isBuryPoint: true,
          quid: result?.visitorId || '',
          from: channel || '',
          source: location?.pathname || '',
          timestamp: +new Date(),
          system: '重疾险',
          requestid: uuidv4(),
          action: 'enter',
          desc: ''
        }
      })
    } catch (e) {}
  }

  useEffect(() => {
    getDeviceInfor()
  }, [location])

  useEffect(() => {
    setTimeout(() => {
      if (deviceInfor?.source) {
        fetch('https://front-log.huijun365.com/static/record?' + qs.stringify(deviceInfor))
          .then(res => {
            console.log(res)
          })
          .catch(e => {
            console.log(e)
          })
      };
    }, 100)
    
    
  }, [deviceInfor])

  return null
}

export default BuryPoint
