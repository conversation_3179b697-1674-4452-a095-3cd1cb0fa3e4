.blue_end_modal_mask {
  box-sizing: border-box;
  padding: 0 36px;
  position: fixed;
  left: 0;
  width: 100%;
  top: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  .blue_end_modal_card {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    background-color: #fff;
    padding: 10px 15px 21px;
    width: 100%;
    background: #FFFFFF;
    border-radius: 10px;
    max-height: 100%;
    // max-height: 100%;
    .blue_end_modal_icon {
      width: 46px;
      height: 46px;
    }
    .blue_end_modal_title {
      margin-top: 10px;
      font-size: 18px;
      font-weight: 500;
      color: #272A33;
      line-height: 25px;
    }
    .blue_end_modal_text {
      margin-top: 11px;
      color: #636477;
      font-size: 13px;
      line-height: 25px;
      flex: 1;
      overflow: auto;
      word-break : normal;
      text-align: justify;
    }
    .blue_end_modal_btn {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 17px 30px 0;
      margin-top: 10px;
      height: 44px;
      line-height: 44px;
      border-radius: 22px;
      background: linear-gradient(85deg, #FC741E 0%, #F3222B 100%);
      filter: blur(0px);
      color: #fff;
      font-size: 16px;
    }
  }
}