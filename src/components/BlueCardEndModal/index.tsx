/*
 * @Author: your name
 * @Date: 2021-09-22 14:39:23
 * @LastEditTime: 2024-09-04 10:07:40
 * @LastEditors: wuqiang
 * @Description: In User Settings Edit
 * @FilePath: /blog/Users/<USER>/workspace/huijun-frontend/src/components/RecordModal/index.tsx
 */
import React, { useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react'
import './index.scss'
import { useHistory, useLocation } from 'react-router-dom'
declare let WeixinJSBridge: any

const BlueCardEndModal = React.forwardRef((props: any, ref) => {
  const { title, message } = props
  const [isShow, setIsShow] = useState(true)
  const location = useLocation()
  const history = useHistory()


  const handleClick = () => {
      try {
        WeixinJSBridge?.call('closeWindow')
      } catch (e) {}
  }

  useEffect(() => {
    document.body.style.overflow = isShow ? 'hidden' : 'auto'
  }, [])

  if (!isShow) return null
  return (
    <div className="blue_end_modal_mask">
      <div className="blue_end_modal_card">
        <h2 className="blue_end_modal_title">{title || '温馨提示'}</h2>
        <p
          className="blue_end_modal_text"
          dangerouslySetInnerHTML={{ __html: message || '目前产品已下线，无法办理，敬请等待！' }}></p>
        <div className="blue_end_modal_btn" onClick={handleClick}>
          我知道了
        </div>
      </div>
    </div>
  )
})

export default BlueCardEndModal
