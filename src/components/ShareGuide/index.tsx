/*
 * @Author: your name
 * @Date: 2021-09-22 18:50:25
 * @LastEditTime: 2021-09-22 19:08:22
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /blog/Users/<USER>/workspace/huijun-frontend/src/components/ShareGuide/index.tsx
 */
import React from 'react';
import './index.scss';
import share_guide from '@/assets/share_guide.png';

function ShareGuide(props) {
  const {isShow, setIsShow} = props;
  return (
    isShow ? (
      <div className="share-guide-page" onClick={() => setIsShow(false)}>
        <img src={share_guide} className="share_guide_img" alt="" />
      </div>
    ) : null
  )
}

export default ShareGuide;