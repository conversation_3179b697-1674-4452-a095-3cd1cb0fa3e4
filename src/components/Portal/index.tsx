/*
 * @message: ~
 * @Version: 1.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @since: 2025-07-20 12:03:19
 * @LastAuthor: Huang<PERSON><PERSON><PERSON>
 * @lastTime: 2025-07-20 12:03:23
 * @FilePath: /cii-front/src/components/Portal/index.tsx
 * 代码如果有bug，自求多福 😊
 */
import React from 'react';
import ReactDOM from 'react-dom';
import './index.scss';

type PortalProps = {
  children: React.ReactNode;
  parentEl?: Element;
};

const Portal = (props: PortalProps) => {
  const { children, parentEl } = props;
  return typeof document === 'object'
    ? ReactDOM.createPortal(children, parentEl || document.body)
    : null;
};

export { Portal };