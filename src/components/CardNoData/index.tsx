/*
 * @Author: your name
 * @Date: 2021-09-26 10:55:39
 * @LastEditTime: 2024-02-28 23:57:21
 * @LastEditors: wuqiang
 * @Description: In User Settings Edit
 * @FilePath: /blog/Users/<USER>/workspace/huijun-frontend/src/components/CardNoData/index.tsx
 */
import React from 'react';
import './index.scss';
import imgNoData from '@/assets/nodata.png';

function CardNoData(props: any) {
  const {text, list} = props;
  return (
    <div className="card-no-data-page">
      <img src={imgNoData} alt="" />
      <p>{ (list && list.length == 0) ? '暂无数据' : text}</p>
    </div>
  )
}

export default CardNoData;