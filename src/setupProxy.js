/*
 * @Author: w<PERSON><PERSON><PERSON>
 * @Date: 2023-08-08 09:15:28
 * @LastEditors: wuqiang
 * @LastEditTime: 2024-07-14 22:43:16
 */
const { createProxyMiddleware } = require('http-proxy-middleware')

module.exports = function (app) {
  app.use(
    createProxyMiddleware('/insur_cii', {
      // 代理服务器地址
      target: 'https://cii.huijun365.com/',
      secure: false,
      changeOrigin: true,
      pathRewrite: {
        '^/insur_cii': '/insur_cii'
      }
    })
  )
}


// /insur_cii/api/policy/check